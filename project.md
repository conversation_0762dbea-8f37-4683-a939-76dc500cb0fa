**Product Requirements Document (PRD) - OmniConnect**

---

### **1. Overview**

**Product Name:** OmniConnect
**Type:** SaaS Business Communication Platform
**Built With:** <PERSON><PERSON> (Backend), SQLite (DB), Tailwind CSS (JIT CDN), <PERSON>ont Awesome (Icons), AJAX, Agent Vibe Coding

**Objective:**
To provide businesses with a unified platform for managing communication across multiple messaging channels (Chat Widget, WhatsApp, Telegram, Messenger, SMS), along with tools such as live chat, no-code chatbot builder, and SMPP gateway for enterprise messaging.

---

### **2. Target Users**

* E-commerce businesses
* Customer support teams
* Marketing teams
* Enterprises with bulk messaging needs

---

### **3. Core Modules & Detailed Functional Requirements**

#### **3.1 Authentication and Onboarding**

* **Vertical login page** (email + password + remember me)
* **Registration page** (Company name, Contact name, Email, Password)
* Email verification
* Password reset with email link
* Role-based redirection after login (<PERSON><PERSON>, <PERSON><PERSON>)

#### **3.2 Channel Manager (Multi-Channel Messaging)**

##### Supported Channels:

* WhatsApp Business API
* Telegram Bot
* Facebook Messenger
* SMS (via SMPP)
* Web Chat Widget

##### Features:

* Enable/disable channels per client
* Token/key setup (API-based integration)
* View channel status (Connected / Not Connected)
* Default sender channel per use case

##### Message Sending:

* Send messages to individual contacts or lists
* Select channel per message
* Schedule messages (cron-based execution)
* Template library (create/edit/delete message templates)
* Message logs (status: sent, delivered, failed, queued)

#### **3.3 SMPP Gateway Module**

##### For Admin:

* Create SMPP client account (system\_id, password, throttle, allowed IPs)
* View active connections
* View message logs (real-time & historical)
* Assign delivery channels based on content type or user segment

##### For Clients:

* SMPP credentials (readonly view)
* Traffic summary: Sent, Failed, Delivery Rate
* Manage sender ID (if supported)

#### **3.4 Live Chat Module**

##### User Roles:

* **Admin**: Create users, assign roles, manage departments
* **Supervisor**: View all chats in dept, rank agents, monitor performance
* **Agent**: Respond to assigned chats, send memos

##### Chat Features:

* Inbox with chat routing (Round Robin or Smart Assignment)
* Chat Tags and Notes
* Memos (visible only to internal team)
* File Attachments
* View Chat History per customer

##### Department Management:

* Create departments (e.g., Sales, Tech Support)
* Assign users to one or multiple departments
* Department-based chat routing

#### **3.5 No-Code Chatbot Builder**

##### Nodes:

* **Bot Message**: Plain text or quick replies
* **User Input**: Capture text, email, phone, etc.
* **API Call**: Trigger external API with parameters
* **Goto**: Jump to another node
* **Condition**: Branch logic based on variables (if/else)
* **Set Variable**: Assign user-defined values
* **Handover to Agent**: Transition to live chat

##### Features:

* Drag-and-drop builder
* Save/load chatbot flows
* Clone bots
* Preview simulation
* Channel-specific bots
* Track conversions per chatbot

#### **3.6 Messaging Center**

* Manual message composer
* Audience segmentation (tags, filters)
* Message personalization using variables (e.g., {{name}})
* Bulk import of contacts (CSV)
* WhatsApp template approval management (Meta API)

#### **3.7 Client Dashboard**

##### Widgets:

* Active channels & status
* Monthly message usage (bar chart)
* SMPP usage summary
* Last 5 invoices
* Support & Helpdesk link

##### Subscriptions:

* Plan type, expiry, renewal options
* Upgrade/Downgrade
* Payment gateway integration (Stripe or PayPal)

##### Invoices:

* View all invoices
* Filter by date/status
* Download PDF
* Payment history

#### **3.8 Admin Control Panel**

* **Client Management**:

  * Add/edit/suspend client
  * View activity logs
  * Assign subscription manually

* **Landing Page Editor**:

  * CMS blocks: Hero, Features, Pricing, FAQ, Contact
  * Upload images
  * Toggle visibility

* **Monitoring:**

  * Active SMPP connections
  * Messages in queue (SMPP + other channels)
  * Chatbot usage report per client
  * Channel errors/warnings log

#### **3.9 Notifications**

* Email and in-app notifications:

  * Message failures
  * New chat
  * Bot errors
  * Payment due

---

### **4. Technical Requirements**

* **Frontend:**

  * Tailwind CSS JIT (CDN)
  * Font Awesome (CDN)
  * Blade templates (Laravel)

* **Backend:**

  * Laravel (latest stable)
  * SQLite (for MVP, can migrate to MySQL/PostgreSQL)

* **Async Tasks:**

  * Laravel queues for sending messages
  * Scheduled jobs for reports and retries

* **APIs:**

  * REST APIs for channels
  * Webhooks for delivery status
  * OAuth2 for 3rd party integrations

* **Security:**

  * RBAC system
  * Token encryption
  * Rate limiting for SMPP

---

### **5. Project Milestones**

| Phase   | Feature Set                               | Duration |
| ------- | ----------------------------------------- | -------- |
| Phase 1 | Auth, Dashboard, Channel Setup, SMPP Core | 2 weeks  |
| Phase 2 | Live Chat System with Roles               | 2 weeks  |
| Phase 3 | Chatbot Builder MVP                       | 3 weeks  |
| Phase 4 | Billing & Subscriptions                   | 1 week   |
| Phase 5 | Admin Panel, Landing Page CMS             | 2 weeks  |
| Phase 6 | Analytics, Webhooks, Notifications        | 2 weeks  |

---

### **6. Additional Notes**

* Devs must modularize components for scalability.
* Frontend should be responsive and minimal.
* Prefer Alpine.js for interactivity (optional).
* Use Laravel policies for access control.
* Testing coverage: Auth, messaging, chatbot flows.

---

Let me know if you want me to add wireframes, database schema, API documentation, or frontend component specs.
