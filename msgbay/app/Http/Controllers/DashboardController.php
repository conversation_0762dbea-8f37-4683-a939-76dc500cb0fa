<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the application dashboard.
     */
    public function index()
    {
        $user = Auth::user();

        // Role-based dashboard views
        return match($user->role) {
            'admin' => view('admin.dashboard'),
            'client' => view('client.dashboard'),
            'supervisor' => view('supervisor.dashboard'),
            'agent' => view('agent.dashboard'),
            default => view('dashboard'),
        };
    }

    /**
     * Show client dashboard.
     */
    public function clientDashboard()
    {
        $user = Auth::user();
        $company = $user->company;

        // Get dashboard data
        $activeChannels = $company->channels()->where('is_enabled', true)->count();
        $monthlyMessages = $company->messages()->whereMonth('created_at', now()->month)->count();
        $activeChats = $company->chats()->where('status', '!=', 'closed')->count();

        return view('client.dashboard', compact(
            'user',
            'company',
            'activeChannels',
            'monthlyMessages',
            'activeChats'
        ));
    }

    /**
     * Show admin dashboard.
     */
    public function adminDashboard()
    {
        // Admin dashboard logic
        return view('admin.dashboard');
    }

    /**
     * Show supervisor dashboard.
     */
    public function supervisorDashboard()
    {
        // Supervisor dashboard logic
        return view('supervisor.dashboard');
    }

    /**
     * Show agent dashboard.
     */
    public function agentDashboard()
    {
        // Agent dashboard logic
        return view('agent.dashboard');
    }
}
