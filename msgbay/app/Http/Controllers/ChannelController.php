<?php

namespace App\Http\Controllers;

use App\Models\Channel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ChannelController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = Auth::user();
        $channels = $user->company->channels()->get();

        return view('channels.index', compact('channels'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $channelTypes = [
            'whatsapp' => 'WhatsApp Business',
            'telegram' => 'Telegram Bot',
            'messenger' => 'Facebook Messenger',
            'sms' => 'SMS Gateway',
            'webchat' => 'Web Chat Widget'
        ];

        return view('channels.create', compact('channelTypes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'type' => 'required|in:whatsapp,telegram,messenger,sms,webchat',
            'name' => 'required|string|max:255',
            'config' => 'required|array',
        ]);

        $user = Auth::user();

        $channel = Channel::create([
            'company_id' => $user->company_id,
            'type' => $request->type,
            'name' => $request->name,
            'config' => $request->config,
            'is_enabled' => false,
            'is_connected' => false,
        ]);

        return redirect()->route('channels.index')
            ->with('success', 'Channel created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Channel $channel)
    {
        $this->authorize('view', $channel);

        return view('channels.show', compact('channel'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Channel $channel)
    {
        $this->authorize('update', $channel);

        return view('channels.edit', compact('channel'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Channel $channel)
    {
        $this->authorize('update', $channel);

        $request->validate([
            'name' => 'required|string|max:255',
            'config' => 'required|array',
        ]);

        $channel->update([
            'name' => $request->name,
            'config' => $request->config,
        ]);

        return redirect()->route('channels.index')
            ->with('success', 'Channel updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Channel $channel)
    {
        $this->authorize('delete', $channel);

        $channel->delete();

        return redirect()->route('channels.index')
            ->with('success', 'Channel deleted successfully!');
    }

    /**
     * Toggle channel enabled status.
     */
    public function toggle(Channel $channel)
    {
        $this->authorize('update', $channel);

        $channel->update([
            'is_enabled' => !$channel->is_enabled
        ]);

        $status = $channel->is_enabled ? 'enabled' : 'disabled';

        return redirect()->route('channels.index')
            ->with('success', "Channel {$status} successfully!");
    }

    /**
     * Test channel connection.
     */
    public function testConnection(Channel $channel)
    {
        $this->authorize('update', $channel);

        // Here you would implement actual connection testing logic
        // For now, we'll just simulate it
        $isConnected = $this->performConnectionTest($channel);

        $channel->update(['is_connected' => $isConnected]);

        $message = $isConnected ? 'Channel connection successful!' : 'Channel connection failed!';
        $type = $isConnected ? 'success' : 'error';

        return redirect()->route('channels.index')
            ->with($type, $message);
    }

    /**
     * Simulate connection test (to be implemented with actual APIs).
     */
    private function performConnectionTest(Channel $channel): bool
    {
        // This is a placeholder - implement actual connection testing
        return true;
    }
}
