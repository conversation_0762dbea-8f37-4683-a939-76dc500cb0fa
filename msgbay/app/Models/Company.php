<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Company extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'contact_name',
        'email',
        'phone',
        'address',
        'is_active',
        'settings',
    ];

    protected function casts(): array
    {
        return [
            'settings' => 'array',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get the users for the company.
     */
    public function users(): Has<PERSON><PERSON>
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the channels for the company.
     */
    public function channels(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Channel::class);
    }

    /**
     * Get the departments for the company.
     */
    public function departments(): HasMany
    {
        return $this->hasMany(Department::class);
    }

    /**
     * Get the chats for the company.
     */
    public function chats(): HasMany
    {
        return $this->hasMany(Chat::class);
    }

    /**
     * Get the messages for the company.
     */
    public function messages(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(Message::class);
    }

    /**
     * Get the chatbots for the company.
     */
    public function chatbots(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Chatbot::class);
    }

    /**
     * Get the SMPP clients for the company.
     */
    public function smppClients(): HasMany
    {
        return $this->hasMany(SmppClient::class);
    }

    /**
     * Get the subscriptions for the company.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Get the templates for the company.
     */
    public function templates(): HasMany
    {
        return $this->hasMany(Template::class);
    }

    /**
     * Get the active subscription for the company.
     */
    public function activeSubscription()
    {
        return $this->subscriptions()->where('status', 'active')->first();
    }
}
