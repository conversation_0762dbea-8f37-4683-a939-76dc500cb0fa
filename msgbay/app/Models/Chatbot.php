<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Chatbot extends Model
{
    use HasFactory;

    protected $fillable = [
        'company_id',
        'channel_id',
        'name',
        'description',
        'flow_data',
        'is_active',
        'settings',
        'conversions',
    ];

    protected function casts(): array
    {
        return [
            'flow_data' => 'array',
            'settings' => 'array',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get the company that owns the chatbot.
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Get the channel that owns the chatbot.
     */
    public function channel(): BelongsTo
    {
        return $this->belongsTo(Channel::class);
    }

    /**
     * Increment conversions count.
     */
    public function incrementConversions(): void
    {
        $this->increment('conversions');
    }
}
