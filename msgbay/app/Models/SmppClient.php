<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SmppClient extends Model
{
    use HasFactory;

    protected $fillable = [
        'company_id',
        'system_id',
        'password',
        'throttle_limit',
        'allowed_ips',
        'is_active',
        'is_connected',
        'messages_sent',
        'messages_failed',
        'delivery_rate',
        'last_connection_at',
    ];

    protected function casts(): array
    {
        return [
            'allowed_ips' => 'array',
            'is_active' => 'boolean',
            'is_connected' => 'boolean',
            'last_connection_at' => 'datetime',
        ];
    }

    /**
     * Get the company that owns the SMPP client.
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Update delivery rate based on sent and failed messages.
     */
    public function updateDeliveryRate(): void
    {
        $total = $this->messages_sent + $this->messages_failed;
        if ($total > 0) {
            $this->delivery_rate = ($this->messages_sent / $total) * 100;
            $this->save();
        }
    }
}
