<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Chat extends Model
{
    use HasFactory;

    protected $fillable = [
        'company_id',
        'channel_id',
        'department_id',
        'assigned_agent_id',
        'customer_name',
        'customer_email',
        'customer_phone',
        'customer_identifier',
        'status',
        'tags',
        'notes',
        'last_activity_at',
    ];

    protected function casts(): array
    {
        return [
            'tags' => 'array',
            'last_activity_at' => 'datetime',
        ];
    }

    /**
     * Get the company that owns the chat.
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Get the channel that owns the chat.
     */
    public function channel(): BelongsTo
    {
        return $this->belongsTo(Channel::class);
    }

    /**
     * Get the department that owns the chat.
     */
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * Get the assigned agent for the chat.
     */
    public function assignedAgent(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_agent_id');
    }

    /**
     * Get the messages for the chat.
     */
    public function messages(): HasMany
    {
        return $this->hasMany(Message::class);
    }

    /**
     * Check if chat is open.
     */
    public function isOpen(): bool
    {
        return $this->status === 'open';
    }

    /**
     * Check if chat is assigned.
     */
    public function isAssigned(): bool
    {
        return $this->status === 'assigned';
    }

    /**
     * Check if chat is closed.
     */
    public function isClosed(): bool
    {
        return $this->status === 'closed';
    }
}
