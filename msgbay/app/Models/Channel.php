<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Channel extends Model
{
    use HasFactory;

    protected $fillable = [
        'company_id',
        'type',
        'name',
        'is_enabled',
        'is_connected',
        'config',
        'settings',
    ];

    protected function casts(): array
    {
        return [
            'config' => 'array',
            'settings' => 'array',
            'is_enabled' => 'boolean',
            'is_connected' => 'boolean',
        ];
    }

    /**
     * Get the company that owns the channel.
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Get the messages for the channel.
     */
    public function messages(): HasMany
    {
        return $this->hasMany(Message::class);
    }

    /**
     * Get the chats for the channel.
     */
    public function chats(): HasMany
    {
        return $this->hasMany(Chat::class);
    }

    /**
     * Get the chatbots for the channel.
     */
    public function chatbots(): Has<PERSON>any
    {
        return $this->hasMany(Chatbot::class);
    }

    /**
     * Check if channel is WhatsApp.
     */
    public function isWhatsApp(): bool
    {
        return $this->type === 'whatsapp';
    }

    /**
     * Check if channel is Telegram.
     */
    public function isTelegram(): bool
    {
        return $this->type === 'telegram';
    }

    /**
     * Check if channel is Messenger.
     */
    public function isMessenger(): bool
    {
        return $this->type === 'messenger';
    }

    /**
     * Check if channel is SMS.
     */
    public function isSms(): bool
    {
        return $this->type === 'sms';
    }

    /**
     * Check if channel is Web Chat.
     */
    public function isWebchat(): bool
    {
        return $this->type === 'webchat';
    }
}
