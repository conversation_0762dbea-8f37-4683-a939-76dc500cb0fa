[2025-07-14 22:09:09] local.ERROR: SQLSTATE[HY000]: General error: 1 table users has no column named role (Connection: sqlite, SQL: insert into "users" ("name", "email", "password", "role", "company_id", "is_active", "updated_at", "created_at") values (<PERSON>, <EMAIL>, $2y$12$2soh27rFA0LUnEQkUuagyOkbJ90v3JN3cnqFTawH7mUB.n0ZBepsO, client, 1, 1, 2025-07-14 22:09:09, 2025-07-14 22:09:09)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 table users has no column named role (Connection: sqlite, SQL: insert into \"users\" (\"name\", \"email\", \"password\", \"role\", \"company_id\", \"is_active\", \"updated_at\", \"created_at\") values (<PERSON>, <EMAIL>, $2y$12$2soh27rFA0LUnEQkUuagyOkbJ90v3JN3cnqFTawH7mUB.n0ZBepsO, client, 1, 1, 2025-07-14 22:09:09, 2025-07-14 22:09:09)) at /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"us...', Array, Object(Closure))
#1 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"us...', Array, Object(Closure))
#2 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"us...', Array)
#3 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Query/Processors/Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"us...', Array)
#4 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"us...', Array, 'id')
#5 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#6 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1431): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1396): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1235): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#10 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Support/helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\User))
#11 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1188): tap(Object(App\\Models\\User), Object(Closure))
#12 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Support/Traits/ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#13 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2517): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#14 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2533): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#15 /Users/<USER>/Herd/msgbay/msgbay/database/seeders/TestDataSeeder.php(30): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#16 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Database\\Seeders\\TestDataSeeder->run()
#17 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#22 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#23 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#24 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#25 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#26 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#27 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#29 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#30 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#31 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#32 /Users/<USER>/Herd/msgbay/msgbay/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#33 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#34 /Users/<USER>/Herd/msgbay/msgbay/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 /Users/<USER>/Herd/msgbay/msgbay/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 /Users/<USER>/Herd/msgbay/msgbay/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 /Users/<USER>/Herd/msgbay/msgbay/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#40 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 table users has no column named role at /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Connection.php:562)
[stacktrace]
#0 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Connection.php(562): PDO->prepare('insert into \"us...')
#1 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into \"us...', Array)
#2 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"us...', Array, Object(Closure))
#3 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"us...', Array, Object(Closure))
#4 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"us...', Array)
#5 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Query/Processors/Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"us...', Array)
#6 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"us...', Array, 'id')
#7 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#8 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1431): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#9 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1396): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#10 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1235): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#11 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#12 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Support/helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\User))
#13 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1188): tap(Object(App\\Models\\User), Object(Closure))
#14 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Support/Traits/ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#15 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2517): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#16 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2533): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#17 /Users/<USER>/Herd/msgbay/msgbay/database/seeders/TestDataSeeder.php(30): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#18 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Database\\Seeders\\TestDataSeeder->run()
#19 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#24 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#25 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#26 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#27 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#28 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#29 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#30 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#31 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#32 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#33 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#34 /Users/<USER>/Herd/msgbay/msgbay/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#35 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#36 /Users/<USER>/Herd/msgbay/msgbay/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 /Users/<USER>/Herd/msgbay/msgbay/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 /Users/<USER>/Herd/msgbay/msgbay/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 /Users/<USER>/Herd/msgbay/msgbay/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 /Users/<USER>/Herd/msgbay/msgbay/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#42 {main}
"} 
