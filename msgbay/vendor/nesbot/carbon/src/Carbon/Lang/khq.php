<?php

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

return array_replace_recursive(require __DIR__.'/en.php', [
    'meridiem' => ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
    'weekdays' => ['Alhadi', '<PERSON><PERSON>', '<PERSON>alata', '<PERSON><PERSON><PERSON>', '<PERSON>hamiisa', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'],
    'weekdays_short' => ['Alh', 'Ati', 'Ata', 'Ala', 'Alm', 'Alj', 'Ass'],
    'weekdays_min' => ['Alh', 'Ati', 'Ata', 'Ala', 'Alm', 'Alj', 'Ass'],
    'months' => ['Žanwiye', '<PERSON><PERSON>iriye', 'Marsi', 'A<PERSON>ril', 'Me', '<PERSON>uwe<PERSON>', '<PERSON>uyye', 'Ut', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
    'months_short' => ['<PERSON><PERSON>', 'Fee', '<PERSON>', 'A<PERSON>', 'Me', '<PERSON>uw', '<PERSON>uy', 'Ut', '<PERSON>k', 'Okt', 'Noo', 'Dee'],
    'first_day_of_week' => 1,
    'formats' => [
        'LT' => 'HH:mm',
        'LTS' => 'HH:mm:ss',
        'L' => 'D/M/YYYY',
        'LL' => 'D MMM, YYYY',
        'LLL' => 'D MMMM YYYY HH:mm',
        'LLLL' => 'dddd D MMMM YYYY HH:mm',
    ],
]);
