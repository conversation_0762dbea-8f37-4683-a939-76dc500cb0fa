<?php

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

return array_replace_recursive(require __DIR__.'/en.php', [
    'meridiem' => ['v.<PERSON>.', 'n.M.'],
    'weekdays' => ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>wo<PERSON>', 'Dunn<PERSON><PERSON><PERSON>', 'Friidaach', 'Samsdaach'],
    'weekdays_short' => ['Su.', 'Mo.', 'Di.', 'Me.', 'Du.', 'Fr.', 'Sa.'],
    'weekdays_min' => ['Su', '<PERSON>', 'Di', 'Me', 'Du', 'Fr', 'Sa'],
    'months' => ['Jannewa', '<PERSON><PERSON><PERSON><PERSON>', 'Mä<PERSON>z', 'Aprell', 'Mai', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>u<PERSON><PERSON>', 'Septämber', '<PERSON><PERSON><PERSON><PERSON>', 'Novämber', '<PERSON><PERSON><PERSON><PERSON>'],
    'months_short' => ['Jan', '<PERSON>ä<PERSON>', '<PERSON><PERSON><PERSON>', 'Apr', '<PERSON>', '<PERSON>', '<PERSON>', 'Ouj', '<PERSON>ä<PERSON>', 'Okt', 'Nov', 'Dez'],
    'months_short_standalone' => ['Jan.', 'Fäb.', 'Mäz.', 'Apr.', 'Mai', 'Jun.', 'Jul.', 'Ouj.', 'Säp.', 'Okt.', 'Nov.', 'Dez.'],
    'first_day_of_week' => 1,
    'formats' => [
        'LT' => 'HH:mm',
        'LTS' => 'HH:mm:ss',
        'L' => 'D. M. YYYY',
        'LL' => 'D. MMM. YYYY',
        'LLL' => 'D. MMMM YYYY HH:mm',
        'LLLL' => 'dddd, [dä] D. MMMM YYYY HH:mm',
    ],

    'year' => ':count Johr',
    'y' => ':count Johr',
    'a_year' => ':count Johr',

    'month' => ':count Moohnd',
    'm' => ':count Moohnd',
    'a_month' => ':count Moohnd',

    'week' => ':count woch',
    'w' => ':count woch',
    'a_week' => ':count woch',

    'day' => ':count Daach',
    'd' => ':count Daach',
    'a_day' => ':count Daach',

    'hour' => ':count Uhr',
    'h' => ':count Uhr',
    'a_hour' => ':count Uhr',

    'minute' => ':count Menutt',
    'min' => ':count Menutt',
    'a_minute' => ':count Menutt',

    'second' => ':count Sekůndt',
    's' => ':count Sekůndt',
    'a_second' => ':count Sekůndt',
]);
