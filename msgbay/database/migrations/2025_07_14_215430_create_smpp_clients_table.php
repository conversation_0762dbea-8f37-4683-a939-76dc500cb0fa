<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('smpp_clients', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->constrained()->onDelete('cascade');
            $table->string('system_id')->unique();
            $table->string('password');
            $table->integer('throttle_limit')->default(10); // Messages per second
            $table->json('allowed_ips')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_connected')->default(false);
            $table->integer('messages_sent')->default(0);
            $table->integer('messages_failed')->default(0);
            $table->decimal('delivery_rate', 5, 2)->default(0);
            $table->timestamp('last_connection_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('smpp_clients');
    }
};
