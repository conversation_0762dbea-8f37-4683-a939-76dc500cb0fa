@extends('layouts.dashboard')

@section('page-title', 'Dashboard')

@section('content')
<div class="p-6">
    <!-- Welcome Section -->
    <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-900">Welcome back, {{ $user->name }}!</h2>
        <p class="text-gray-600">Here's what's happening with your MessageBay account today.</p>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Active Channels -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <i class="fas fa-comments text-blue-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Active Channels</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $activeChannels ?? 0 }}</p>
                </div>
            </div>
        </div>

        <!-- Monthly Messages -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <i class="fas fa-envelope text-green-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Messages This Month</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $monthlyMessages ?? 0 }}</p>
                </div>
            </div>
        </div>

        <!-- Active Chats -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 rounded-lg">
                    <i class="fas fa-headset text-yellow-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Active Chats</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $activeChats ?? 0 }}</p>
                </div>
            </div>
        </div>

        <!-- Subscription Status -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 rounded-lg">
                    <i class="fas fa-crown text-purple-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Plan Status</p>
                    <p class="text-2xl font-bold text-gray-900">
                        @if($company->activeSubscription())
                            Active
                        @else
                            Free Trial
                        @endif
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Channel Setup -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Setup</h3>
            <div class="space-y-3">
                <a href="#" class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <i class="fab fa-whatsapp text-green-500 text-xl mr-3"></i>
                    <div>
                        <p class="font-medium text-gray-900">Connect WhatsApp</p>
                        <p class="text-sm text-gray-600">Set up WhatsApp Business API</p>
                    </div>
                </a>
                <a href="#" class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <i class="fab fa-telegram text-blue-500 text-xl mr-3"></i>
                    <div>
                        <p class="font-medium text-gray-900">Connect Telegram</p>
                        <p class="text-sm text-gray-600">Set up Telegram Bot</p>
                    </div>
                </a>
                <a href="#" class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <i class="fas fa-robot text-purple-500 text-xl mr-3"></i>
                    <div>
                        <p class="font-medium text-gray-900">Create Chatbot</p>
                        <p class="text-sm text-gray-600">Build your first chatbot</p>
                    </div>
                </a>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
            <div class="space-y-3">
                <div class="flex items-center">
                    <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900">Account created successfully</p>
                        <p class="text-xs text-gray-500">{{ $user->created_at->diffForHumans() }}</p>
                    </div>
                </div>
                <div class="flex items-center">
                    <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900">Welcome to MessageBay!</p>
                        <p class="text-xs text-gray-500">Get started by connecting your first channel</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Help Section -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div class="flex items-center">
            <i class="fas fa-info-circle text-blue-600 text-xl mr-3"></i>
            <div>
                <h3 class="text-lg font-semibold text-blue-900">Need Help Getting Started?</h3>
                <p class="text-blue-700 mt-1">Check out our documentation and tutorials to make the most of MessageBay.</p>
                <div class="mt-4 space-x-4">
                    <a href="#" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700">
                        <i class="fas fa-book mr-2"></i>
                        Documentation
                    </a>
                    <a href="#" class="inline-flex items-center px-4 py-2 bg-white text-blue-600 text-sm font-medium rounded-lg border border-blue-600 hover:bg-blue-50">
                        <i class="fas fa-play mr-2"></i>
                        Video Tutorials
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
