@extends('layouts.dashboard')

@section('page-title', 'Add Channel')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center space-x-2 text-sm text-gray-600 mb-2">
            <a href="{{ route('channels.index') }}" class="hover:text-blue-600">Channels</a>
            <i class="fas fa-chevron-right text-xs"></i>
            <span>Add Channel</span>
        </div>
        <h2 class="text-2xl font-bold text-gray-900">Add New Channel</h2>
        <p class="text-gray-600">Connect a new messaging channel to your MessageBay account</p>
    </div>

    <div class="max-w-2xl">
        <form action="{{ route('channels.store') }}" method="POST" x-data="channelForm()">
            @csrf
            
            <!-- Channel Type Selection -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-3">Select Channel Type</label>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    @foreach($channelTypes as $type => $label)
                        <label class="relative cursor-pointer">
                            <input type="radio" name="type" value="{{ $type }}" 
                                   x-model="selectedType"
                                   class="sr-only peer" required>
                            <div class="p-4 border-2 border-gray-200 rounded-lg peer-checked:border-blue-500 peer-checked:bg-blue-50 hover:border-gray-300">
                                <div class="flex items-center">
                                    @switch($type)
                                        @case('whatsapp')
                                            <i class="fab fa-whatsapp text-green-500 text-2xl mr-3"></i>
                                            @break
                                        @case('telegram')
                                            <i class="fab fa-telegram text-blue-500 text-2xl mr-3"></i>
                                            @break
                                        @case('messenger')
                                            <i class="fab fa-facebook-messenger text-blue-600 text-2xl mr-3"></i>
                                            @break
                                        @case('sms')
                                            <i class="fas fa-sms text-purple-500 text-2xl mr-3"></i>
                                            @break
                                        @case('webchat')
                                            <i class="fas fa-comments text-gray-500 text-2xl mr-3"></i>
                                            @break
                                    @endswitch
                                    <div>
                                        <p class="font-medium text-gray-900">{{ $label }}</p>
                                        <p class="text-sm text-gray-600">
                                            @switch($type)
                                                @case('whatsapp')
                                                    Connect WhatsApp Business API
                                                    @break
                                                @case('telegram')
                                                    Create and connect Telegram Bot
                                                    @break
                                                @case('messenger')
                                                    Connect Facebook Messenger
                                                    @break
                                                @case('sms')
                                                    SMS Gateway integration
                                                    @break
                                                @case('webchat')
                                                    Embed chat widget on website
                                                    @break
                                            @endswitch
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </label>
                    @endforeach
                </div>
                @error('type')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Channel Name -->
            <div class="mb-6">
                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Channel Name</label>
                <input type="text" id="name" name="name" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('name') border-red-500 @enderror"
                       placeholder="Enter a name for this channel" value="{{ old('name') }}" required>
                @error('name')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Dynamic Configuration Fields -->
            <div x-show="selectedType" x-cloak>
                <!-- WhatsApp Configuration -->
                <div x-show="selectedType === 'whatsapp'" class="space-y-4 mb-6">
                    <h3 class="text-lg font-medium text-gray-900">WhatsApp Business API Configuration</h3>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number ID</label>
                        <input type="text" name="config[phone_number_id]" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="Your WhatsApp Phone Number ID">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Access Token</label>
                        <input type="password" name="config[access_token]" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="Your WhatsApp Access Token">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Webhook Verify Token</label>
                        <input type="text" name="config[verify_token]" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="Webhook verification token">
                    </div>
                </div>

                <!-- Telegram Configuration -->
                <div x-show="selectedType === 'telegram'" class="space-y-4 mb-6">
                    <h3 class="text-lg font-medium text-gray-900">Telegram Bot Configuration</h3>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Bot Token</label>
                        <input type="password" name="config[bot_token]" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="Your Telegram Bot Token">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Bot Username</label>
                        <input type="text" name="config[bot_username]" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="@your_bot_username">
                    </div>
                </div>

                <!-- Messenger Configuration -->
                <div x-show="selectedType === 'messenger'" class="space-y-4 mb-6">
                    <h3 class="text-lg font-medium text-gray-900">Facebook Messenger Configuration</h3>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Page Access Token</label>
                        <input type="password" name="config[page_access_token]" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="Your Facebook Page Access Token">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">App Secret</label>
                        <input type="password" name="config[app_secret]" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="Your Facebook App Secret">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Verify Token</label>
                        <input type="text" name="config[verify_token]" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="Webhook verification token">
                    </div>
                </div>

                <!-- SMS Configuration -->
                <div x-show="selectedType === 'sms'" class="space-y-4 mb-6">
                    <h3 class="text-lg font-medium text-gray-900">SMS Gateway Configuration</h3>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Provider</label>
                        <select name="config[provider]" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Select SMS Provider</option>
                            <option value="twilio">Twilio</option>
                            <option value="nexmo">Vonage (Nexmo)</option>
                            <option value="smpp">SMPP Gateway</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">API Key</label>
                        <input type="password" name="config[api_key]" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="Your SMS provider API key">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">API Secret</label>
                        <input type="password" name="config[api_secret]" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="Your SMS provider API secret">
                    </div>
                </div>

                <!-- Web Chat Configuration -->
                <div x-show="selectedType === 'webchat'" class="space-y-4 mb-6">
                    <h3 class="text-lg font-medium text-gray-900">Web Chat Widget Configuration</h3>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Widget Title</label>
                        <input type="text" name="config[widget_title]" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="Chat with us!" value="Chat with us!">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Welcome Message</label>
                        <textarea name="config[welcome_message]" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="Welcome! How can we help you today?">Welcome! How can we help you today?</textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Primary Color</label>
                        <input type="color" name="config[primary_color]" value="#3B82F6"
                               class="w-20 h-10 border border-gray-300 rounded-lg">
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                <a href="{{ route('channels.index') }}" 
                   class="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200">
                    Cancel
                </a>
                <button type="submit" 
                        class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    Create Channel
                </button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
function channelForm() {
    return {
        selectedType: ''
    }
}
</script>
@endpush
@endsection
