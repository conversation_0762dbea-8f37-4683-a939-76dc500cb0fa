@extends('layouts.dashboard')

@section('page-title', 'Channel Details')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center space-x-2 text-sm text-gray-600 mb-2">
            <a href="{{ route('channels.index') }}" class="hover:text-blue-600">Channels</a>
            <i class="fas fa-chevron-right text-xs"></i>
            <span>{{ $channel->name }}</span>
        </div>
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">{{ $channel->name }}</h2>
                <p class="text-gray-600 capitalize">{{ str_replace('_', ' ', $channel->type) }} Channel</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('channels.edit', $channel) }}" 
                   class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700">
                    <i class="fas fa-edit mr-2"></i>
                    Edit Channel
                </a>
                <form action="{{ route('channels.toggle', $channel) }}" method="POST" class="inline">
                    @csrf
                    <button type="submit" 
                            class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg {{ $channel->is_enabled ? 'bg-red-600 hover:bg-red-700 text-white' : 'bg-green-600 hover:bg-green-700 text-white' }}">
                        @if($channel->is_enabled)
                            <i class="fas fa-pause mr-2"></i>
                            Disable Channel
                        @else
                            <i class="fas fa-play mr-2"></i>
                            Enable Channel
                        @endif
                    </button>
                </form>
            </div>
        </div>
    </div>

    @if(session('success'))
        <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
            {{ session('success') }}
        </div>
    @endif

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Channel Overview -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Channel Overview</h3>
                
                <div class="grid grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Channel Type</label>
                        <div class="flex items-center">
                            @switch($channel->type)
                                @case('whatsapp')
                                    <i class="fab fa-whatsapp text-green-500 text-xl mr-2"></i>
                                    <span>WhatsApp Business</span>
                                    @break
                                @case('telegram')
                                    <i class="fab fa-telegram text-blue-500 text-xl mr-2"></i>
                                    <span>Telegram Bot</span>
                                    @break
                                @case('messenger')
                                    <i class="fab fa-facebook-messenger text-blue-600 text-xl mr-2"></i>
                                    <span>Facebook Messenger</span>
                                    @break
                                @case('sms')
                                    <i class="fas fa-sms text-purple-500 text-xl mr-2"></i>
                                    <span>SMS Gateway</span>
                                    @break
                                @case('webchat')
                                    <i class="fas fa-comments text-gray-500 text-xl mr-2"></i>
                                    <span>Web Chat Widget</span>
                                    @break
                            @endswitch
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <div class="flex space-x-2">
                            @if($channel->is_enabled)
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    Enabled
                                </span>
                            @else
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    <i class="fas fa-pause-circle mr-1"></i>
                                    Disabled
                                </span>
                            @endif
                            
                            @if($channel->is_connected)
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <i class="fas fa-wifi mr-1"></i>
                                    Connected
                                </span>
                            @else
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <i class="fas fa-wifi-slash mr-1"></i>
                                    Disconnected
                                </span>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="mt-6 pt-6 border-t border-gray-200">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Created</label>
                    <p class="text-gray-900">{{ $channel->created_at->format('M d, Y \a\t g:i A') }}</p>
                </div>

                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Last Updated</label>
                    <p class="text-gray-900">{{ $channel->updated_at->format('M d, Y \a\t g:i A') }}</p>
                </div>
            </div>

            <!-- Configuration Details -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Configuration</h3>
                
                @if($channel->config)
                    <div class="space-y-4">
                        @foreach($channel->config as $key => $value)
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    {{ ucwords(str_replace('_', ' ', $key)) }}
                                </label>
                                @if(str_contains($key, 'token') || str_contains($key, 'secret') || str_contains($key, 'password'))
                                    <p class="text-gray-900 font-mono">{{ str_repeat('•', 20) }}</p>
                                @else
                                    <p class="text-gray-900">{{ is_array($value) ? json_encode($value) : $value }}</p>
                                @endif
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500">No configuration data available.</p>
                @endif
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                <div class="space-y-3">
                    <form action="{{ route('channels.test', $channel) }}" method="POST">
                        @csrf
                        <button type="submit" 
                                class="w-full flex items-center justify-center px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-lg hover:bg-purple-700">
                            <i class="fas fa-plug mr-2"></i>
                            Test Connection
                        </button>
                    </form>
                    
                    <a href="{{ route('channels.edit', $channel) }}" 
                       class="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Configuration
                    </a>
                    
                    @if($channel->type === 'webchat')
                        <button onclick="showEmbedCode()" 
                                class="w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700">
                            <i class="fas fa-code mr-2"></i>
                            Get Embed Code
                        </button>
                    @endif
                </div>
            </div>

            <!-- Statistics -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Statistics</h3>
                <div class="space-y-4">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Messages Sent</span>
                        <span class="text-sm font-medium text-gray-900">{{ $channel->messages()->where('direction', 'outbound')->count() }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Messages Received</span>
                        <span class="text-sm font-medium text-gray-900">{{ $channel->messages()->where('direction', 'inbound')->count() }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Active Chats</span>
                        <span class="text-sm font-medium text-gray-900">{{ $channel->chats()->where('status', '!=', 'closed')->count() }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@if($channel->type === 'webchat')
<!-- Embed Code Modal -->
<div id="embedModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Web Chat Embed Code</h3>
                    <button onclick="hideEmbedCode()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <p class="text-gray-600 mb-4">Copy and paste this code into your website's HTML, just before the closing &lt;/body&gt; tag:</p>
                <div class="bg-gray-100 rounded-lg p-4">
                    <code class="text-sm text-gray-800">
&lt;script&gt;<br>
&nbsp;&nbsp;(function() {<br>
&nbsp;&nbsp;&nbsp;&nbsp;var script = document.createElement('script');<br>
&nbsp;&nbsp;&nbsp;&nbsp;script.src = '{{ url('/') }}/widget/{{ $channel->id }}.js';<br>
&nbsp;&nbsp;&nbsp;&nbsp;document.head.appendChild(script);<br>
&nbsp;&nbsp;})();<br>
&lt;/script&gt;
                    </code>
                </div>
                <div class="mt-4 flex justify-end">
                    <button onclick="copyEmbedCode()" 
                            class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700">
                        Copy Code
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
@endif

@push('scripts')
<script>
function showEmbedCode() {
    document.getElementById('embedModal').classList.remove('hidden');
}

function hideEmbedCode() {
    document.getElementById('embedModal').classList.add('hidden');
}

function copyEmbedCode() {
    const code = `<script>
  (function() {
    var script = document.createElement('script');
    script.src = '{{ url('/') }}/widget/{{ $channel->id }}.js';
    document.head.appendChild(script);
  })();
</script>`;
    
    navigator.clipboard.writeText(code).then(function() {
        alert('Embed code copied to clipboard!');
    });
}
</script>
@endpush
@endsection
