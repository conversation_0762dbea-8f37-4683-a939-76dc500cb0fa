@extends('layouts.dashboard')

@section('page-title', 'Channels')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h2 class="text-2xl font-bold text-gray-900">Communication Channels</h2>
            <p class="text-gray-600">Manage your messaging channels and integrations</p>
        </div>
        <a href="{{ route('channels.create') }}" 
           class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700">
            <i class="fas fa-plus mr-2"></i>
            Add Channel
        </a>
    </div>

    @if(session('success'))
        <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
            {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
            {{ session('error') }}
        </div>
    @endif

    <!-- Channels Grid -->
    @if($channels->count() > 0)
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($channels as $channel)
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <!-- Channel Header -->
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            @switch($channel->type)
                                @case('whatsapp')
                                    <i class="fab fa-whatsapp text-green-500 text-2xl mr-3"></i>
                                    @break
                                @case('telegram')
                                    <i class="fab fa-telegram text-blue-500 text-2xl mr-3"></i>
                                    @break
                                @case('messenger')
                                    <i class="fab fa-facebook-messenger text-blue-600 text-2xl mr-3"></i>
                                    @break
                                @case('sms')
                                    <i class="fas fa-sms text-purple-500 text-2xl mr-3"></i>
                                    @break
                                @case('webchat')
                                    <i class="fas fa-comments text-gray-500 text-2xl mr-3"></i>
                                    @break
                            @endswitch
                            <div>
                                <h3 class="font-semibold text-gray-900">{{ $channel->name }}</h3>
                                <p class="text-sm text-gray-500 capitalize">{{ str_replace('_', ' ', $channel->type) }}</p>
                            </div>
                        </div>
                        
                        <!-- Status Badges -->
                        <div class="flex flex-col items-end space-y-1">
                            @if($channel->is_enabled)
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    Enabled
                                </span>
                            @else
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    <i class="fas fa-pause-circle mr-1"></i>
                                    Disabled
                                </span>
                            @endif
                            
                            @if($channel->is_connected)
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <i class="fas fa-wifi mr-1"></i>
                                    Connected
                                </span>
                            @else
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <i class="fas fa-wifi-slash mr-1"></i>
                                    Disconnected
                                </span>
                            @endif
                        </div>
                    </div>

                    <!-- Channel Actions -->
                    <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                        <div class="flex space-x-2">
                            <a href="{{ route('channels.edit', $channel) }}" 
                               class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                <i class="fas fa-edit mr-1"></i>
                                Edit
                            </a>
                            <a href="{{ route('channels.show', $channel) }}" 
                               class="text-gray-600 hover:text-gray-800 text-sm font-medium">
                                <i class="fas fa-eye mr-1"></i>
                                View
                            </a>
                        </div>
                        
                        <div class="flex space-x-2">
                            <!-- Toggle Button -->
                            <form action="{{ route('channels.toggle', $channel) }}" method="POST" class="inline">
                                @csrf
                                <button type="submit" 
                                        class="text-sm font-medium {{ $channel->is_enabled ? 'text-red-600 hover:text-red-800' : 'text-green-600 hover:text-green-800' }}">
                                    @if($channel->is_enabled)
                                        <i class="fas fa-pause mr-1"></i>
                                        Disable
                                    @else
                                        <i class="fas fa-play mr-1"></i>
                                        Enable
                                    @endif
                                </button>
                            </form>
                            
                            <!-- Test Connection -->
                            <form action="{{ route('channels.test', $channel) }}" method="POST" class="inline">
                                @csrf
                                <button type="submit" 
                                        class="text-sm font-medium text-purple-600 hover:text-purple-800">
                                    <i class="fas fa-plug mr-1"></i>
                                    Test
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    @else
        <!-- Empty State -->
        <div class="text-center py-12">
            <div class="mx-auto h-24 w-24 text-gray-400 mb-4">
                <i class="fas fa-comments text-6xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No channels configured</h3>
            <p class="text-gray-600 mb-6">Get started by adding your first communication channel.</p>
            <a href="{{ route('channels.create') }}" 
               class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700">
                <i class="fas fa-plus mr-2"></i>
                Add Your First Channel
            </a>
        </div>
    @endif
</div>
@endsection
