<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;

class LandingPageController extends Controller
{
    /**
     * Display the landing page editor.
     */
    public function index()
    {
        $content = $this->getLandingPageContent();
        
        return view('admin.landing-page.index', compact('content'));
    }

    /**
     * Update the landing page content.
     */
    public function update(Request $request)
    {
        $request->validate([
            'hero_title' => 'required|string|max:255',
            'hero_subtitle' => 'required|string|max:500',
            'hero_cta_text' => 'required|string|max:50',
            'hero_cta_url' => 'required|url',
            'hero_image' => 'nullable|image|max:2048',
            
            'features_title' => 'required|string|max:255',
            'features_subtitle' => 'nullable|string|max:500',
            
            'feature_1_title' => 'required|string|max:100',
            'feature_1_description' => 'required|string|max:300',
            'feature_1_icon' => 'required|string|max:50',
            
            'feature_2_title' => 'required|string|max:100',
            'feature_2_description' => 'required|string|max:300',
            'feature_2_icon' => 'required|string|max:50',
            
            'feature_3_title' => 'required|string|max:100',
            'feature_3_description' => 'required|string|max:300',
            'feature_3_icon' => 'required|string|max:50',
            
            'about_title' => 'required|string|max:255',
            'about_content' => 'required|string|max:2000',
            'about_image' => 'nullable|image|max:2048',
            
            'contact_title' => 'required|string|max:255',
            'contact_subtitle' => 'nullable|string|max:500',
            'contact_email' => 'required|email',
            'contact_phone' => 'nullable|string|max:20',
            'contact_address' => 'nullable|string|max:300',
            
            'footer_text' => 'required|string|max:200',
            'footer_links' => 'nullable|array',
            'footer_links.*.title' => 'required|string|max:50',
            'footer_links.*.url' => 'required|url',
            
            'meta_title' => 'required|string|max:60',
            'meta_description' => 'required|string|max:160',
            'meta_keywords' => 'nullable|string|max:255',
        ]);

        $content = $request->except(['hero_image', 'about_image']);

        // Handle hero image upload
        if ($request->hasFile('hero_image')) {
            $heroImagePath = $request->file('hero_image')->store('landing-page', 'public');
            $content['hero_image'] = $heroImagePath;
        }

        // Handle about image upload
        if ($request->hasFile('about_image')) {
            $aboutImagePath = $request->file('about_image')->store('landing-page', 'public');
            $content['about_image'] = $aboutImagePath;
        }

        // Save content to storage
        $this->saveLandingPageContent($content);

        // Clear cache
        Cache::forget('landing_page_content');

        return redirect()->route('admin.landing-page.index')
            ->with('success', 'Landing page updated successfully!');
    }

    /**
     * Preview the landing page with current content.
     */
    public function preview(Request $request)
    {
        $content = $request->all();
        
        // Store preview content temporarily
        Cache::put('landing_page_preview', $content, 300); // 5 minutes

        return response()->json([
            'success' => true,
            'preview_url' => route('welcome') . '?preview=true'
        ]);
    }

    /**
     * Get landing page content from storage.
     */
    private function getLandingPageContent()
    {
        $defaultContent = $this->getDefaultContent();
        
        if (Storage::disk('local')->exists('landing-page-content.json')) {
            $stored = json_decode(Storage::disk('local')->get('landing-page-content.json'), true);
            return array_merge($defaultContent, $stored);
        }

        return $defaultContent;
    }

    /**
     * Save landing page content to storage.
     */
    private function saveLandingPageContent($content)
    {
        Storage::disk('local')->put('landing-page-content.json', json_encode($content, JSON_PRETTY_PRINT));
    }

    /**
     * Get default landing page content.
     */
    private function getDefaultContent()
    {
        return [
            // Hero Section
            'hero_title' => 'Welcome to MessageBay',
            'hero_subtitle' => 'The ultimate messaging platform for businesses to connect with their customers across multiple channels.',
            'hero_cta_text' => 'Get Started',
            'hero_cta_url' => '/register',
            'hero_image' => null,

            // Features Section
            'features_title' => 'Why Choose MessageBay?',
            'features_subtitle' => 'Powerful features designed to streamline your customer communication',

            'feature_1_title' => 'Multi-Channel Support',
            'feature_1_description' => 'Connect with customers via WhatsApp, SMS, Email, and Web Chat from a single platform.',
            'feature_1_icon' => 'fas fa-comments',

            'feature_2_title' => 'Real-time Analytics',
            'feature_2_description' => 'Track message delivery, response times, and customer engagement with detailed analytics.',
            'feature_2_icon' => 'fas fa-chart-bar',

            'feature_3_title' => 'Team Collaboration',
            'feature_3_description' => 'Enable your team to collaborate effectively with shared inboxes and assignment features.',
            'feature_3_icon' => 'fas fa-users',

            // About Section
            'about_title' => 'About MessageBay',
            'about_content' => 'MessageBay is a comprehensive messaging platform designed to help businesses communicate more effectively with their customers. Our platform integrates multiple communication channels into a single, easy-to-use interface, making it simple for businesses to manage all their customer interactions in one place.',
            'about_image' => null,

            // Contact Section
            'contact_title' => 'Get in Touch',
            'contact_subtitle' => 'Ready to transform your customer communication? Contact us today!',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '+****************',
            'contact_address' => '123 Business Ave, Suite 100, City, State 12345',

            // Footer
            'footer_text' => '© 2024 MessageBay. All rights reserved.',
            'footer_links' => [
                ['title' => 'Privacy Policy', 'url' => '/privacy'],
                ['title' => 'Terms of Service', 'url' => '/terms'],
                ['title' => 'Support', 'url' => '/support'],
            ],

            // SEO Meta
            'meta_title' => 'MessageBay - Multi-Channel Messaging Platform',
            'meta_description' => 'Connect with customers across WhatsApp, SMS, Email, and Web Chat. Streamline your business communication with MessageBay.',
            'meta_keywords' => 'messaging, WhatsApp, SMS, email, customer communication, business chat',
        ];
    }

    /**
     * Get available icons for features.
     */
    public function getAvailableIcons()
    {
        return [
            'fas fa-comments' => 'Comments',
            'fas fa-chart-bar' => 'Chart Bar',
            'fas fa-users' => 'Users',
            'fas fa-mobile-alt' => 'Mobile',
            'fas fa-envelope' => 'Envelope',
            'fas fa-globe' => 'Globe',
            'fas fa-shield-alt' => 'Shield',
            'fas fa-clock' => 'Clock',
            'fas fa-bolt' => 'Bolt',
            'fas fa-heart' => 'Heart',
            'fas fa-star' => 'Star',
            'fas fa-thumbs-up' => 'Thumbs Up',
            'fas fa-rocket' => 'Rocket',
            'fas fa-cog' => 'Settings',
            'fas fa-lock' => 'Lock',
            'fas fa-check-circle' => 'Check Circle',
            'fas fa-lightbulb' => 'Lightbulb',
            'fas fa-handshake' => 'Handshake',
            'fas fa-trophy' => 'Trophy',
            'fas fa-magic' => 'Magic',
        ];
    }

    /**
     * Reset landing page to default content.
     */
    public function reset()
    {
        Storage::disk('local')->delete('landing-page-content.json');
        Cache::forget('landing_page_content');

        return redirect()->route('admin.landing-page.index')
            ->with('success', 'Landing page reset to default content!');
    }

    /**
     * Export landing page content.
     */
    public function export()
    {
        $content = $this->getLandingPageContent();
        
        $filename = 'landing_page_content_' . now()->format('Y-m-d_H-i-s') . '.json';
        
        return response()->json($content)
            ->header('Content-Disposition', "attachment; filename=\"{$filename}\"");
    }

    /**
     * Import landing page content.
     */
    public function import(Request $request)
    {
        $request->validate([
            'content_file' => 'required|file|mimes:json',
        ]);

        try {
            $content = json_decode($request->file('content_file')->get(), true);
            
            if (!$content) {
                throw new \Exception('Invalid JSON file');
            }

            $this->saveLandingPageContent($content);
            Cache::forget('landing_page_content');

            return redirect()->route('admin.landing-page.index')
                ->with('success', 'Landing page content imported successfully!');
        } catch (\Exception $e) {
            return redirect()->route('admin.landing-page.index')
                ->with('error', 'Failed to import content: ' . $e->getMessage());
        }
    }

    /**
     * Get content for public landing page.
     */
    public static function getPublicContent()
    {
        // Check for preview content first
        if (request()->has('preview') && Cache::has('landing_page_preview')) {
            return Cache::get('landing_page_preview');
        }

        // Get cached content or load from storage
        return Cache::remember('landing_page_content', 3600, function () {
            $controller = new self();
            return $controller->getLandingPageContent();
        });
    }
}
