<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\Company;
use App\Models\Message;
use App\Models\Channel;
use Carbon\Carbon;

class ReportsController extends Controller
{
    /**
     * Display the reports dashboard.
     */
    public function index()
    {
        $overview = $this->getOverviewStats();
        $recentActivity = $this->getRecentActivity();
        $topClients = $this->getTopClients();
        $channelStats = $this->getChannelStats();

        return view('admin.reports.index', compact(
            'overview',
            'recentActivity',
            'topClients',
            'channelStats'
        ));
    }

    /**
     * Display detailed analytics.
     */
    public function analytics(Request $request)
    {
        $period = $request->get('period', '30days');
        $dateRange = $this->getDateRange($period);

        $analytics = [
            'user_growth' => $this->getUserGrowthData($dateRange),
            'message_volume' => $this->getMessageVolumeData($dateRange),
            'channel_performance' => $this->getChannelPerformanceData($dateRange),
            'client_activity' => $this->getClientActivityData($dateRange),
            'response_times' => $this->getResponseTimeData($dateRange),
        ];

        return view('admin.reports.analytics', compact('analytics', 'period'));
    }

    /**
     * Export reports data.
     */
    public function export($type, Request $request)
    {
        $period = $request->get('period', '30days');
        $format = $request->get('format', 'csv');

        switch ($type) {
            case 'users':
                return $this->exportUsers($period, $format);
            case 'messages':
                return $this->exportMessages($period, $format);
            case 'clients':
                return $this->exportClients($period, $format);
            case 'analytics':
                return $this->exportAnalytics($period, $format);
            default:
                abort(404);
        }
    }

    /**
     * Get overview statistics.
     */
    private function getOverviewStats()
    {
        $today = Carbon::today();
        $yesterday = Carbon::yesterday();
        $lastWeek = Carbon::now()->subWeek();
        $lastMonth = Carbon::now()->subMonth();

        return [
            'total_users' => [
                'count' => User::count(),
                'change' => $this->calculatePercentageChange(
                    User::where('created_at', '>=', $lastWeek)->count(),
                    User::where('created_at', '>=', $lastWeek->copy()->subWeek())->where('created_at', '<', $lastWeek)->count()
                ),
            ],
            'total_companies' => [
                'count' => Company::count(),
                'change' => $this->calculatePercentageChange(
                    Company::where('created_at', '>=', $lastWeek)->count(),
                    Company::where('created_at', '>=', $lastWeek->copy()->subWeek())->where('created_at', '<', $lastWeek)->count()
                ),
            ],
            'total_messages' => [
                'count' => Message::count(),
                'change' => $this->calculatePercentageChange(
                    Message::where('created_at', '>=', $today)->count(),
                    Message::whereDate('created_at', $yesterday)->count()
                ),
            ],
            'active_channels' => [
                'count' => Channel::where('is_enabled', true)->count(),
                'change' => 0, // Channels don't change frequently
            ],
        ];
    }

    /**
     * Get recent activity.
     */
    private function getRecentActivity()
    {
        return [
            'recent_users' => User::latest()->limit(5)->get(),
            'recent_companies' => Company::latest()->limit(5)->get(),
            'recent_messages' => Message::with(['company', 'channel'])->latest()->limit(10)->get(),
        ];
    }

    /**
     * Get top clients by message volume.
     */
    private function getTopClients()
    {
        return Company::withCount('messages')
            ->orderBy('messages_count', 'desc')
            ->limit(10)
            ->get();
    }

    /**
     * Get channel statistics.
     */
    private function getChannelStats()
    {
        return Channel::withCount('messages')
            ->get()
            ->groupBy('type')
            ->map(function ($channels, $type) {
                return [
                    'type' => $type,
                    'count' => $channels->count(),
                    'total_messages' => $channels->sum('messages_count'),
                    'enabled' => $channels->where('is_enabled', true)->count(),
                ];
            });
    }

    /**
     * Get user growth data.
     */
    private function getUserGrowthData($dateRange)
    {
        $data = [];
        $current = $dateRange['start']->copy();

        while ($current <= $dateRange['end']) {
            $data[] = [
                'date' => $current->format('Y-m-d'),
                'users' => User::whereDate('created_at', $current)->count(),
                'companies' => Company::whereDate('created_at', $current)->count(),
            ];
            $current->addDay();
        }

        return $data;
    }

    /**
     * Get message volume data.
     */
    private function getMessageVolumeData($dateRange)
    {
        $data = [];
        $current = $dateRange['start']->copy();

        while ($current <= $dateRange['end']) {
            $data[] = [
                'date' => $current->format('Y-m-d'),
                'messages' => Message::whereDate('created_at', $current)->count(),
                'unique_senders' => Message::whereDate('created_at', $current)
                    ->distinct('company_id')
                    ->count('company_id'),
            ];
            $current->addDay();
        }

        return $data;
    }

    /**
     * Get channel performance data.
     */
    private function getChannelPerformanceData($dateRange)
    {
        return Channel::withCount([
            'messages' => function ($query) use ($dateRange) {
                $query->whereBetween('created_at', [$dateRange['start'], $dateRange['end']]);
            }
        ])->get();
    }

    /**
     * Get client activity data.
     */
    private function getClientActivityData($dateRange)
    {
        return Company::withCount([
            'messages' => function ($query) use ($dateRange) {
                $query->whereBetween('created_at', [$dateRange['start'], $dateRange['end']]);
            }
        ])
        ->orderBy('messages_count', 'desc')
        ->limit(20)
        ->get();
    }

    /**
     * Get response time data (mock data for now).
     */
    private function getResponseTimeData($dateRange)
    {
        // In a real implementation, you would track actual response times
        $data = [];
        $current = $dateRange['start']->copy();

        while ($current <= $dateRange['end']) {
            $data[] = [
                'date' => $current->format('Y-m-d'),
                'avg_response_time' => rand(30, 300), // seconds
                'messages_count' => Message::whereDate('created_at', $current)->count(),
            ];
            $current->addDay();
        }

        return $data;
    }

    /**
     * Get date range based on period.
     */
    private function getDateRange($period)
    {
        $end = Carbon::now();
        
        switch ($period) {
            case '7days':
                $start = Carbon::now()->subDays(7);
                break;
            case '30days':
                $start = Carbon::now()->subDays(30);
                break;
            case '90days':
                $start = Carbon::now()->subDays(90);
                break;
            case '1year':
                $start = Carbon::now()->subYear();
                break;
            default:
                $start = Carbon::now()->subDays(30);
        }

        return ['start' => $start, 'end' => $end];
    }

    /**
     * Calculate percentage change.
     */
    private function calculatePercentageChange($current, $previous)
    {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }

        return round((($current - $previous) / $previous) * 100, 1);
    }

    /**
     * Export users data.
     */
    private function exportUsers($period, $format)
    {
        $dateRange = $this->getDateRange($period);
        $users = User::with('company')
            ->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
            ->get();

        return $this->generateExport($users, 'users', $format, [
            'ID', 'Name', 'Email', 'Role', 'Company', 'Status', 'Created At'
        ], function ($user) {
            return [
                $user->id,
                $user->name,
                $user->email,
                $user->role,
                $user->company ? $user->company->name : 'N/A',
                $user->is_active ? 'Active' : 'Inactive',
                $user->created_at->format('Y-m-d H:i:s'),
            ];
        });
    }

    /**
     * Export messages data.
     */
    private function exportMessages($period, $format)
    {
        $dateRange = $this->getDateRange($period);
        $messages = Message::with(['company', 'channel'])
            ->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
            ->get();

        return $this->generateExport($messages, 'messages', $format, [
            'ID', 'Company', 'Channel', 'Type', 'Content', 'Status', 'Created At'
        ], function ($message) {
            return [
                $message->id,
                $message->company ? $message->company->name : 'N/A',
                $message->channel ? $message->channel->name : 'N/A',
                $message->type ?? 'text',
                substr($message->content, 0, 100) . (strlen($message->content) > 100 ? '...' : ''),
                $message->status ?? 'sent',
                $message->created_at->format('Y-m-d H:i:s'),
            ];
        });
    }

    /**
     * Export clients data.
     */
    private function exportClients($period, $format)
    {
        $dateRange = $this->getDateRange($period);
        $clients = Company::withCount('messages', 'users')
            ->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
            ->get();

        return $this->generateExport($clients, 'clients', $format, [
            'ID', 'Name', 'Email', 'Contact Name', 'Phone', 'Users Count', 'Messages Count', 'Status', 'Created At'
        ], function ($client) {
            return [
                $client->id,
                $client->name,
                $client->email,
                $client->contact_name,
                $client->phone,
                $client->users_count,
                $client->messages_count,
                $client->is_active ? 'Active' : 'Inactive',
                $client->created_at->format('Y-m-d H:i:s'),
            ];
        });
    }

    /**
     * Export analytics data.
     */
    private function exportAnalytics($period, $format)
    {
        $dateRange = $this->getDateRange($period);
        $analytics = $this->getUserGrowthData($dateRange);

        return $this->generateExport($analytics, 'analytics', $format, [
            'Date', 'New Users', 'New Companies', 'Messages'
        ], function ($data) {
            return [
                $data['date'],
                $data['users'],
                $data['companies'],
                Message::whereDate('created_at', $data['date'])->count(),
            ];
        });
    }

    /**
     * Generate export file.
     */
    private function generateExport($data, $type, $format, $headers, $rowCallback)
    {
        $filename = "{$type}_export_" . now()->format('Y-m-d_H-i-s') . ".{$format}";

        if ($format === 'csv') {
            $csvHeaders = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => "attachment; filename=\"{$filename}\"",
            ];

            $callback = function() use ($data, $headers, $rowCallback) {
                $file = fopen('php://output', 'w');
                fputcsv($file, $headers);

                foreach ($data as $item) {
                    fputcsv($file, $rowCallback($item));
                }

                fclose($file);
            };

            return response()->stream($callback, 200, $csvHeaders);
        }

        // JSON format
        $jsonData = $data->map($rowCallback);
        
        return response()->json($jsonData)
            ->header('Content-Disposition', "attachment; filename=\"{$filename}\"");
    }
}
