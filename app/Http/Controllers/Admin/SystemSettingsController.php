<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Artisan;

class SystemSettingsController extends Controller
{
    /**
     * Display the system settings.
     */
    public function index()
    {
        $settings = $this->getSystemSettings();
        
        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Update system settings.
     */
    public function update(Request $request)
    {
        $request->validate([
            // General Settings
            'app_name' => 'required|string|max:255',
            'app_url' => 'required|url',
            'app_timezone' => 'required|string',
            'app_locale' => 'required|string|max:5',
            
            // Email Settings
            'mail_driver' => 'required|string',
            'mail_host' => 'required_if:mail_driver,smtp|string',
            'mail_port' => 'required_if:mail_driver,smtp|integer',
            'mail_username' => 'required_if:mail_driver,smtp|string',
            'mail_password' => 'nullable|string',
            'mail_encryption' => 'nullable|string',
            'mail_from_address' => 'required|email',
            'mail_from_name' => 'required|string|max:255',
            
            // SMS Settings
            'sms_provider' => 'required|string',
            'sms_api_key' => 'nullable|string',
            'sms_api_secret' => 'nullable|string',
            'sms_from_number' => 'nullable|string',
            
            // WhatsApp Settings
            'whatsapp_api_url' => 'nullable|url',
            'whatsapp_access_token' => 'nullable|string',
            'whatsapp_verify_token' => 'nullable|string',
            'whatsapp_phone_number_id' => 'nullable|string',
            
            // Security Settings
            'session_lifetime' => 'required|integer|min:1|max:10080',
            'password_min_length' => 'required|integer|min:6|max:50',
            'max_login_attempts' => 'required|integer|min:1|max:20',
            'lockout_duration' => 'required|integer|min:1|max:1440',
            
            // File Upload Settings
            'max_file_size' => 'required|integer|min:1|max:100',
            'allowed_file_types' => 'required|string',
            
            // API Settings
            'api_rate_limit' => 'required|integer|min:10|max:10000',
            'api_timeout' => 'required|integer|min:5|max:300',
            
            // Notification Settings
            'enable_email_notifications' => 'boolean',
            'enable_sms_notifications' => 'boolean',
            'enable_push_notifications' => 'boolean',
            
            // Maintenance Settings
            'maintenance_mode' => 'boolean',
            'maintenance_message' => 'nullable|string|max:500',
            
            // Backup Settings
            'auto_backup_enabled' => 'boolean',
            'backup_frequency' => 'required|string',
            'backup_retention_days' => 'required|integer|min:1|max:365',
        ]);

        $settings = $request->except(['_token', '_method']);
        
        // Don't update password if it's empty
        if (empty($settings['mail_password'])) {
            unset($settings['mail_password']);
        }

        $this->saveSystemSettings($settings);

        // Clear relevant caches
        Cache::forget('system_settings');
        
        // Update environment variables if needed
        $this->updateEnvironmentVariables($settings);

        return redirect()->route('admin.settings.index')
            ->with('success', 'System settings updated successfully!');
    }

    /**
     * Test email configuration.
     */
    public function testEmail(Request $request)
    {
        $request->validate([
            'test_email' => 'required|email',
        ]);

        try {
            Mail::raw('This is a test email from MessageBay system.', function ($message) use ($request) {
                $message->to($request->test_email)
                        ->subject('MessageBay - Test Email');
            });

            return response()->json([
                'success' => true,
                'message' => 'Test email sent successfully!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send test email: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get system settings from storage.
     */
    private function getSystemSettings()
    {
        $defaultSettings = $this->getDefaultSettings();
        
        if (Storage::disk('local')->exists('system-settings.json')) {
            $stored = json_decode(Storage::disk('local')->get('system-settings.json'), true);
            return array_merge($defaultSettings, $stored);
        }

        return $defaultSettings;
    }

    /**
     * Save system settings to storage.
     */
    private function saveSystemSettings($settings)
    {
        Storage::disk('local')->put('system-settings.json', json_encode($settings, JSON_PRETTY_PRINT));
    }

    /**
     * Get default system settings.
     */
    private function getDefaultSettings()
    {
        return [
            // General Settings
            'app_name' => config('app.name', 'MessageBay'),
            'app_url' => config('app.url', 'http://localhost'),
            'app_timezone' => config('app.timezone', 'UTC'),
            'app_locale' => config('app.locale', 'en'),
            
            // Email Settings
            'mail_driver' => config('mail.default', 'smtp'),
            'mail_host' => config('mail.mailers.smtp.host', 'localhost'),
            'mail_port' => config('mail.mailers.smtp.port', 587),
            'mail_username' => config('mail.mailers.smtp.username', ''),
            'mail_password' => '', // Don't expose actual password
            'mail_encryption' => config('mail.mailers.smtp.encryption', 'tls'),
            'mail_from_address' => config('mail.from.address', '<EMAIL>'),
            'mail_from_name' => config('mail.from.name', 'MessageBay'),
            
            // SMS Settings
            'sms_provider' => 'twilio',
            'sms_api_key' => '',
            'sms_api_secret' => '',
            'sms_from_number' => '',
            
            // WhatsApp Settings
            'whatsapp_api_url' => 'https://graph.facebook.com/v18.0',
            'whatsapp_access_token' => '',
            'whatsapp_verify_token' => '',
            'whatsapp_phone_number_id' => '',
            
            // Security Settings
            'session_lifetime' => config('session.lifetime', 120),
            'password_min_length' => 8,
            'max_login_attempts' => 5,
            'lockout_duration' => 15,
            
            // File Upload Settings
            'max_file_size' => 10, // MB
            'allowed_file_types' => 'jpg,jpeg,png,gif,pdf,doc,docx,txt',
            
            // API Settings
            'api_rate_limit' => 1000,
            'api_timeout' => 30,
            
            // Notification Settings
            'enable_email_notifications' => true,
            'enable_sms_notifications' => false,
            'enable_push_notifications' => false,
            
            // Maintenance Settings
            'maintenance_mode' => false,
            'maintenance_message' => 'We are currently performing scheduled maintenance. Please check back soon.',
            
            // Backup Settings
            'auto_backup_enabled' => false,
            'backup_frequency' => 'daily',
            'backup_retention_days' => 30,
        ];
    }

    /**
     * Update environment variables.
     */
    private function updateEnvironmentVariables($settings)
    {
        $envUpdates = [
            'APP_NAME' => $settings['app_name'] ?? null,
            'APP_URL' => $settings['app_url'] ?? null,
            'APP_TIMEZONE' => $settings['app_timezone'] ?? null,
            'MAIL_MAILER' => $settings['mail_driver'] ?? null,
            'MAIL_HOST' => $settings['mail_host'] ?? null,
            'MAIL_PORT' => $settings['mail_port'] ?? null,
            'MAIL_USERNAME' => $settings['mail_username'] ?? null,
            'MAIL_FROM_ADDRESS' => $settings['mail_from_address'] ?? null,
            'MAIL_FROM_NAME' => $settings['mail_from_name'] ?? null,
        ];

        // Only update password if provided
        if (!empty($settings['mail_password'])) {
            $envUpdates['MAIL_PASSWORD'] = $settings['mail_password'];
        }

        // Filter out null values
        $envUpdates = array_filter($envUpdates, function($value) {
            return $value !== null;
        });

        // Update .env file (simplified approach)
        foreach ($envUpdates as $key => $value) {
            $this->updateEnvFile($key, $value);
        }
    }

    /**
     * Update a single environment variable in .env file.
     */
    private function updateEnvFile($key, $value)
    {
        $envFile = base_path('.env');
        
        if (!file_exists($envFile)) {
            return;
        }

        $content = file_get_contents($envFile);
        $value = '"' . str_replace('"', '\"', $value) . '"';
        
        if (strpos($content, $key . '=') !== false) {
            // Update existing key
            $content = preg_replace(
                '/^' . preg_quote($key) . '=.*$/m',
                $key . '=' . $value,
                $content
            );
        } else {
            // Add new key
            $content .= "\n" . $key . '=' . $value;
        }

        file_put_contents($envFile, $content);
    }

    /**
     * Clear application cache.
     */
    public function clearCache()
    {
        try {
            Artisan::call('cache:clear');
            Artisan::call('config:clear');
            Artisan::call('view:clear');
            Artisan::call('route:clear');

            return response()->json([
                'success' => true,
                'message' => 'Application cache cleared successfully!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear cache: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get system information.
     */
    public function getSystemInfo()
    {
        return [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'operating_system' => PHP_OS,
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
            'timezone' => date_default_timezone_get(),
            'disk_free_space' => $this->formatBytes(disk_free_space('/')),
            'disk_total_space' => $this->formatBytes(disk_total_space('/')),
        ];
    }

    /**
     * Format bytes to human readable format.
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Export system settings.
     */
    public function export()
    {
        $settings = $this->getSystemSettings();
        
        // Remove sensitive data
        unset($settings['mail_password']);
        unset($settings['sms_api_secret']);
        unset($settings['whatsapp_access_token']);
        
        $filename = 'system_settings_' . now()->format('Y-m-d_H-i-s') . '.json';
        
        return response()->json($settings)
            ->header('Content-Disposition', "attachment; filename=\"{$filename}\"");
    }

    /**
     * Reset settings to default.
     */
    public function reset()
    {
        Storage::disk('local')->delete('system-settings.json');
        Cache::forget('system_settings');

        return redirect()->route('admin.settings.index')
            ->with('success', 'System settings reset to default values!');
    }
}
