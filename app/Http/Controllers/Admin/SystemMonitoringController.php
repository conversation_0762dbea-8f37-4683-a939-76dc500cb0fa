<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use App\Models\User;
use App\Models\Company;
use App\Models\Message;
use App\Models\Channel;

class SystemMonitoringController extends Controller
{
    /**
     * Display the system monitoring dashboard.
     */
    public function index()
    {
        $systemHealth = $this->getSystemHealth();
        $performanceMetrics = $this->getPerformanceMetrics();
        $realtimeStats = $this->getRealtimeStats();
        $serverInfo = $this->getServerInfo();

        return view('admin.monitoring.index', compact(
            'systemHealth',
            'performanceMetrics',
            'realtimeStats',
            'serverInfo'
        ));
    }

    /**
     * Get detailed system health information.
     */
    public function systemHealth()
    {
        $health = $this->getSystemHealth();
        $services = $this->getServiceStatus();
        $diskUsage = $this->getDiskUsage();
        $memoryUsage = $this->getMemoryUsage();

        return view('admin.monitoring.system-health', compact(
            'health',
            'services',
            'diskUsage',
            'memoryUsage'
        ));
    }

    /**
     * Get performance metrics.
     */
    public function performance()
    {
        $metrics = $this->getPerformanceMetrics();
        $responseTime = $this->getResponseTimeMetrics();
        $throughput = $this->getThroughputMetrics();
        $errorRates = $this->getErrorRates();

        return view('admin.monitoring.performance', compact(
            'metrics',
            'responseTime',
            'throughput',
            'errorRates'
        ));
    }

    /**
     * Get system health status.
     */
    private function getSystemHealth()
    {
        $health = [
            'overall_status' => 'healthy',
            'database' => $this->checkDatabaseHealth(),
            'cache' => $this->checkCacheHealth(),
            'storage' => $this->checkStorageHealth(),
            'queue' => $this->checkQueueHealth(),
        ];

        // Determine overall status
        $statuses = array_values($health);
        if (in_array('critical', $statuses)) {
            $health['overall_status'] = 'critical';
        } elseif (in_array('warning', $statuses)) {
            $health['overall_status'] = 'warning';
        }

        return $health;
    }

    /**
     * Get performance metrics.
     */
    private function getPerformanceMetrics()
    {
        return [
            'cpu_usage' => $this->getCpuUsage(),
            'memory_usage' => $this->getMemoryUsage(),
            'disk_usage' => $this->getDiskUsage(),
            'response_time' => $this->getAverageResponseTime(),
            'active_connections' => $this->getActiveConnections(),
        ];
    }

    /**
     * Get real-time statistics.
     */
    private function getRealtimeStats()
    {
        return [
            'active_users' => User::where('is_active', true)->count(),
            'total_companies' => Company::where('is_active', true)->count(),
            'messages_today' => Message::whereDate('created_at', today())->count(),
            'active_channels' => Channel::where('is_enabled', true)->count(),
            'system_uptime' => $this->getSystemUptime(),
        ];
    }

    /**
     * Get server information.
     */
    private function getServerInfo()
    {
        return [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'operating_system' => PHP_OS,
            'timezone' => config('app.timezone'),
            'environment' => config('app.env'),
        ];
    }

    /**
     * Check database health.
     */
    private function checkDatabaseHealth()
    {
        try {
            DB::connection()->getPdo();
            $responseTime = $this->measureDatabaseResponseTime();
            
            if ($responseTime > 1000) {
                return 'warning';
            }
            return 'healthy';
        } catch (\Exception $e) {
            return 'critical';
        }
    }

    /**
     * Check cache health.
     */
    private function checkCacheHealth()
    {
        try {
            Cache::put('health_check', 'test', 60);
            $value = Cache::get('health_check');
            return $value === 'test' ? 'healthy' : 'warning';
        } catch (\Exception $e) {
            return 'critical';
        }
    }

    /**
     * Check storage health.
     */
    private function checkStorageHealth()
    {
        $diskUsage = $this->getDiskUsage();
        if ($diskUsage['percentage'] > 90) {
            return 'critical';
        } elseif ($diskUsage['percentage'] > 80) {
            return 'warning';
        }
        return 'healthy';
    }

    /**
     * Check queue health.
     */
    private function checkQueueHealth()
    {
        // For now, assume healthy since we don't have queue monitoring
        return 'healthy';
    }

    /**
     * Get CPU usage (mock data for now).
     */
    private function getCpuUsage()
    {
        // In a real implementation, you would get actual CPU usage
        return rand(10, 80);
    }

    /**
     * Get memory usage.
     */
    private function getMemoryUsage()
    {
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = $this->convertToBytes(ini_get('memory_limit'));
        
        return [
            'used' => $memoryUsage,
            'limit' => $memoryLimit,
            'percentage' => round(($memoryUsage / $memoryLimit) * 100, 2),
            'used_formatted' => $this->formatBytes($memoryUsage),
            'limit_formatted' => $this->formatBytes($memoryLimit),
        ];
    }

    /**
     * Get disk usage.
     */
    private function getDiskUsage()
    {
        $totalSpace = disk_total_space('/');
        $freeSpace = disk_free_space('/');
        $usedSpace = $totalSpace - $freeSpace;
        
        return [
            'total' => $totalSpace,
            'used' => $usedSpace,
            'free' => $freeSpace,
            'percentage' => round(($usedSpace / $totalSpace) * 100, 2),
            'total_formatted' => $this->formatBytes($totalSpace),
            'used_formatted' => $this->formatBytes($usedSpace),
            'free_formatted' => $this->formatBytes($freeSpace),
        ];
    }

    /**
     * Get average response time.
     */
    private function getAverageResponseTime()
    {
        // Mock data - in real implementation, you'd track this
        return rand(50, 200);
    }

    /**
     * Get active connections.
     */
    private function getActiveConnections()
    {
        // Mock data - in real implementation, you'd get actual connection count
        return rand(10, 100);
    }

    /**
     * Get system uptime.
     */
    private function getSystemUptime()
    {
        // Mock data - in real implementation, you'd get actual uptime
        return '5 days, 12 hours, 30 minutes';
    }

    /**
     * Measure database response time.
     */
    private function measureDatabaseResponseTime()
    {
        $start = microtime(true);
        DB::select('SELECT 1');
        $end = microtime(true);
        
        return round(($end - $start) * 1000, 2); // Convert to milliseconds
    }

    /**
     * Get service status.
     */
    private function getServiceStatus()
    {
        return [
            'web_server' => 'running',
            'database' => 'running',
            'cache' => 'running',
            'queue_worker' => 'stopped',
            'scheduler' => 'running',
        ];
    }

    /**
     * Get response time metrics.
     */
    private function getResponseTimeMetrics()
    {
        // Mock data for demonstration
        return [
            'average' => 150,
            'p95' => 300,
            'p99' => 500,
            'min' => 50,
            'max' => 800,
        ];
    }

    /**
     * Get throughput metrics.
     */
    private function getThroughputMetrics()
    {
        return [
            'requests_per_minute' => rand(100, 500),
            'messages_per_minute' => rand(50, 200),
            'api_calls_per_minute' => rand(20, 100),
        ];
    }

    /**
     * Get error rates.
     */
    private function getErrorRates()
    {
        return [
            'http_4xx' => rand(1, 5),
            'http_5xx' => rand(0, 2),
            'database_errors' => 0,
            'cache_errors' => 0,
        ];
    }

    /**
     * Convert memory limit string to bytes.
     */
    private function convertToBytes($value)
    {
        $value = trim($value);
        $last = strtolower($value[strlen($value) - 1]);
        $value = (int) $value;

        switch ($last) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }

        return $value;
    }

    /**
     * Format bytes to human readable format.
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
