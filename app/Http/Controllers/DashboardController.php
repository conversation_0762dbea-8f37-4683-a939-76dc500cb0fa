<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class DashboardController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the application dashboard.
     */
    public function index()
    {
        $user = Auth::user();

        // Role-based dashboard views
        return match($user->role) {
            'admin' => redirect()->route('admin.dashboard'),
            'client' => redirect()->route('client.dashboard'),
            'supervisor' => view('supervisor.dashboard'),
            'agent' => view('agent.dashboard'),
            default => view('dashboard'),
        };
    }

    /**
     * Show client dashboard.
     */
    public function clientDashboard()
    {
        $user = Auth::user();
        $company = $user->company;

        // Get dashboard data
        $activeChannels = $company->channels()->where('is_enabled', true)->count();
        $monthlyMessages = $company->messages()->whereMonth('created_at', now()->month)->count();
        $activeChats = $company->chats()->where('status', '!=', 'closed')->count();

        return view('client.dashboard', compact(
            'user',
            'company',
            'activeChannels',
            'monthlyMessages',
            'activeChats'
        ));
    }

    /**
     * Show admin dashboard.
     */
    public function adminDashboard()
    {
        // Admin dashboard logic
        return view('admin.dashboard');
    }

    /**
     * Show supervisor dashboard.
     */
    public function supervisorDashboard()
    {
        // Supervisor dashboard logic
        return view('supervisor.dashboard');
    }

    /**
     * Show agent dashboard.
     */
    public function agentDashboard()
    {
        // Agent dashboard logic
        return view('agent.dashboard');
    }

    /**
     * Show user profile.
     */
    public function profile()
    {
        $user = Auth::user();
        return view('profile.show', compact('user'));
    }

    /**
     * Show edit profile form.
     */
    public function editProfile()
    {
        $user = Auth::user();
        return view('profile.edit', compact('user'));
    }

    /**
     * Update user profile.
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'email', Rule::unique('users')->ignore($user->id)],
            'phone' => 'nullable|string|max:20',
            'avatar' => 'nullable|image|max:2048',
        ]);

        $data = $request->only(['name', 'email', 'phone']);

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            $avatarPath = $request->file('avatar')->store('avatars', 'public');
            $data['avatar'] = $avatarPath;
        }

        $user->update($data);

        return redirect()->route('profile.show')
            ->with('success', 'Profile updated successfully!');
    }

    /**
     * Update user password.
     */
    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required',
            'password' => 'required|string|min:8|confirmed',
        ]);

        $user = Auth::user();

        if (!Hash::check($request->current_password, $user->password)) {
            return back()->withErrors(['current_password' => 'Current password is incorrect.']);
        }

        $user->update([
            'password' => Hash::make($request->password)
        ]);

        return redirect()->route('profile.show')
            ->with('success', 'Password updated successfully!');
    }

    /**
     * Show user settings.
     */
    public function settings()
    {
        $user = Auth::user();
        $settings = $user->settings ?? [];

        return view('settings.index', compact('user', 'settings'));
    }

    /**
     * Update user settings.
     */
    public function updateSettings(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'timezone' => 'required|string',
            'language' => 'required|string',
            'email_notifications' => 'boolean',
            'sms_notifications' => 'boolean',
            'push_notifications' => 'boolean',
            'theme' => 'required|in:light,dark,auto',
        ]);

        $settings = [
            'timezone' => $request->timezone,
            'language' => $request->language,
            'email_notifications' => $request->boolean('email_notifications'),
            'sms_notifications' => $request->boolean('sms_notifications'),
            'push_notifications' => $request->boolean('push_notifications'),
            'theme' => $request->theme,
        ];

        $user->update(['settings' => $settings]);

        return redirect()->route('user.settings.index')
            ->with('success', 'Settings updated successfully!');
    }
}
