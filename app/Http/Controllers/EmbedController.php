<?php

namespace App\Http\Controllers;

use App\Models\Channel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class EmbedController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display embed code management interface.
     */
    public function index()
    {
        $user = Auth::user();
        $webChatChannels = $user->company->channels()
            ->where('type', 'webchat')
            ->where('is_enabled', true)
            ->get();

        return view('embed.index', compact('webChatChannels'));
    }

    /**
     * Generate embed code for a channel.
     */
    public function generate(Request $request)
    {
        $request->validate([
            'channel_id' => 'required|exists:channels,id',
            'widget_position' => 'required|in:bottom-right,bottom-left,top-right,top-left',
            'widget_color' => 'required|string|max:7',
            'widget_title' => 'required|string|max:100',
            'welcome_message' => 'required|string|max:500',
            'offline_message' => 'nullable|string|max:500',
            'show_agent_avatar' => 'boolean',
            'show_typing_indicator' => 'boolean',
            'enable_file_upload' => 'boolean',
            'enable_emoji' => 'boolean',
        ]);

        $user = Auth::user();
        
        // Verify channel belongs to user's company
        $channel = Channel::where('id', $request->channel_id)
            ->where('company_id', $user->company_id)
            ->where('type', 'webchat')
            ->firstOrFail();

        // Generate unique token for this embed configuration
        $token = Str::random(32);

        // Store embed configuration
        $embedConfig = [
            'channel_id' => $channel->id,
            'company_id' => $user->company_id,
            'widget_position' => $request->widget_position,
            'widget_color' => $request->widget_color,
            'widget_title' => $request->widget_title,
            'welcome_message' => $request->welcome_message,
            'offline_message' => $request->offline_message ?? 'We are currently offline. Please leave a message.',
            'show_agent_avatar' => $request->boolean('show_agent_avatar'),
            'show_typing_indicator' => $request->boolean('show_typing_indicator'),
            'enable_file_upload' => $request->boolean('enable_file_upload'),
            'enable_emoji' => $request->boolean('enable_emoji'),
            'created_at' => now(),
        ];

        // Update channel config with embed settings
        $channelConfig = $channel->config;
        $channelConfig['embed_configs'][$token] = $embedConfig;
        $channel->update(['config' => $channelConfig]);

        // Generate embed code
        $embedCode = $this->generateEmbedCode($token, $embedConfig);

        return response()->json([
            'success' => true,
            'token' => $token,
            'embed_code' => $embedCode,
            'preview_url' => route('embed.preview', $token),
            'config' => $embedConfig
        ]);
    }

    /**
     * Preview embed widget.
     */
    public function preview($token)
    {
        // Find the embed configuration
        $embedConfig = $this->findEmbedConfig($token);
        
        if (!$embedConfig) {
            abort(404, 'Embed configuration not found');
        }

        return view('embed.preview', compact('embedConfig', 'token'));
    }

    /**
     * Serve the widget JavaScript.
     */
    public function widget($token)
    {
        $embedConfig = $this->findEmbedConfig($token);
        
        if (!$embedConfig) {
            return response('// Embed configuration not found', 404)
                ->header('Content-Type', 'application/javascript');
        }

        $widgetJs = $this->generateWidgetJavaScript($token, $embedConfig);

        return response($widgetJs)
            ->header('Content-Type', 'application/javascript')
            ->header('Cache-Control', 'public, max-age=3600');
    }

    /**
     * Handle widget API requests.
     */
    public function api(Request $request, $token)
    {
        $embedConfig = $this->findEmbedConfig($token);
        
        if (!$embedConfig) {
            return response()->json(['error' => 'Invalid token'], 404);
        }

        $action = $request->input('action');

        switch ($action) {
            case 'init':
                return $this->handleInit($embedConfig);
            case 'send_message':
                return $this->handleSendMessage($request, $embedConfig);
            case 'get_messages':
                return $this->handleGetMessages($request, $embedConfig);
            default:
                return response()->json(['error' => 'Invalid action'], 400);
        }
    }

    /**
     * Generate embed code HTML/JavaScript.
     */
    private function generateEmbedCode($token, $embedConfig): string
    {
        $widgetUrl = route('embed.widget', $token);
        $apiUrl = route('embed.api', $token);

        return <<<HTML
<!-- MessageBay Web Chat Widget -->
<script>
(function() {
    var script = document.createElement('script');
    script.type = 'text/javascript';
    script.async = true;
    script.src = '{$widgetUrl}';
    script.onload = function() {
        if (typeof MessageBayWidget !== 'undefined') {
            MessageBayWidget.init({
                token: '{$token}',
                apiUrl: '{$apiUrl}',
                position: '{$embedConfig['widget_position']}',
                color: '{$embedConfig['widget_color']}',
                title: '{$embedConfig['widget_title']}',
                welcomeMessage: '{$embedConfig['welcome_message']}',
                offlineMessage: '{$embedConfig['offline_message']}',
                showAgentAvatar: {$this->boolToJs($embedConfig['show_agent_avatar'])},
                showTypingIndicator: {$this->boolToJs($embedConfig['show_typing_indicator'])},
                enableFileUpload: {$this->boolToJs($embedConfig['enable_file_upload'])},
                enableEmoji: {$this->boolToJs($embedConfig['enable_emoji'])}
            });
        }
    };
    var firstScript = document.getElementsByTagName('script')[0];
    firstScript.parentNode.insertBefore(script, firstScript);
})();
</script>
<!-- End MessageBay Web Chat Widget -->
HTML;
    }

    /**
     * Generate widget JavaScript code.
     */
    private function generateWidgetJavaScript($token, $embedConfig): string
    {
        return <<<JS
(function() {
    'use strict';
    
    window.MessageBayWidget = {
        config: {},
        isOpen: false,
        
        init: function(config) {
            this.config = config;
            this.createWidget();
            this.bindEvents();
        },
        
        createWidget: function() {
            // Create widget HTML structure
            var widgetHtml = this.getWidgetHtml();
            document.body.insertAdjacentHTML('beforeend', widgetHtml);
        },
        
        getWidgetHtml: function() {
            var position = this.config.position || 'bottom-right';
            var color = this.config.color || '#007bff';
            
            return '<div id="messagebay-widget" class="messagebay-widget messagebay-' + position + '" style="--primary-color: ' + color + '">' +
                '<div id="messagebay-toggle" class="messagebay-toggle">' +
                    '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">' +
                        '<path d="M20 2H4C2.9 2 2 2.9 2 4V22L6 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z" fill="white"/>' +
                    '</svg>' +
                '</div>' +
                '<div id="messagebay-chat" class="messagebay-chat" style="display: none;">' +
                    '<div class="messagebay-header">' +
                        '<span class="messagebay-title">' + this.config.title + '</span>' +
                        '<button id="messagebay-close" class="messagebay-close">×</button>' +
                    '</div>' +
                    '<div class="messagebay-messages" id="messagebay-messages">' +
                        '<div class="messagebay-message messagebay-bot">' +
                            '<div class="messagebay-message-content">' + this.config.welcomeMessage + '</div>' +
                        '</div>' +
                    '</div>' +
                    '<div class="messagebay-input-area">' +
                        '<input type="text" id="messagebay-input" placeholder="Type a message..." />' +
                        '<button id="messagebay-send">Send</button>' +
                    '</div>' +
                '</div>' +
            '</div>' + this.getWidgetStyles();
        },
        
        getWidgetStyles: function() {
            return '<style>' +
                '.messagebay-widget { position: fixed; z-index: 9999; font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; }' +
                '.messagebay-bottom-right { bottom: 20px; right: 20px; }' +
                '.messagebay-bottom-left { bottom: 20px; left: 20px; }' +
                '.messagebay-top-right { top: 20px; right: 20px; }' +
                '.messagebay-top-left { top: 20px; left: 20px; }' +
                '.messagebay-toggle { width: 60px; height: 60px; border-radius: 50%; background: var(--primary-color); cursor: pointer; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 12px rgba(0,0,0,0.15); }' +
                '.messagebay-chat { width: 350px; height: 500px; background: white; border-radius: 12px; box-shadow: 0 8px 24px rgba(0,0,0,0.15); display: flex; flex-direction: column; }' +
                '.messagebay-header { background: var(--primary-color); color: white; padding: 16px; border-radius: 12px 12px 0 0; display: flex; justify-content: space-between; align-items: center; }' +
                '.messagebay-title { font-weight: 600; }' +
                '.messagebay-close { background: none; border: none; color: white; font-size: 24px; cursor: pointer; }' +
                '.messagebay-messages { flex: 1; padding: 16px; overflow-y: auto; }' +
                '.messagebay-message { margin-bottom: 12px; }' +
                '.messagebay-message-content { padding: 8px 12px; border-radius: 18px; max-width: 80%; }' +
                '.messagebay-bot .messagebay-message-content { background: #f1f3f5; }' +
                '.messagebay-user .messagebay-message-content { background: var(--primary-color); color: white; margin-left: auto; }' +
                '.messagebay-input-area { padding: 16px; border-top: 1px solid #e9ecef; display: flex; gap: 8px; }' +
                '.messagebay-input-area input { flex: 1; padding: 8px 12px; border: 1px solid #dee2e6; border-radius: 20px; outline: none; }' +
                '.messagebay-input-area button { padding: 8px 16px; background: var(--primary-color); color: white; border: none; border-radius: 20px; cursor: pointer; }' +
            '</style>';
        },
        
        bindEvents: function() {
            var self = this;
            
            document.getElementById('messagebay-toggle').addEventListener('click', function() {
                self.toggleChat();
            });
            
            document.getElementById('messagebay-close').addEventListener('click', function() {
                self.closeChat();
            });
            
            document.getElementById('messagebay-send').addEventListener('click', function() {
                self.sendMessage();
            });
            
            document.getElementById('messagebay-input').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    self.sendMessage();
                }
            });
        },
        
        toggleChat: function() {
            var chat = document.getElementById('messagebay-chat');
            if (this.isOpen) {
                chat.style.display = 'none';
                this.isOpen = false;
            } else {
                chat.style.display = 'flex';
                this.isOpen = true;
            }
        },
        
        closeChat: function() {
            document.getElementById('messagebay-chat').style.display = 'none';
            this.isOpen = false;
        },
        
        sendMessage: function() {
            var input = document.getElementById('messagebay-input');
            var message = input.value.trim();
            if (message) {
                this.addMessage(message, 'user');
                input.value = '';
                
                // Send to API
                this.sendToAPI(message);
            }
        },
        
        addMessage: function(content, type) {
            var messages = document.getElementById('messagebay-messages');
            var messageDiv = document.createElement('div');
            messageDiv.className = 'messagebay-message messagebay-' + type;
            messageDiv.innerHTML = '<div class="messagebay-message-content">' + content + '</div>';
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        },
        
        sendToAPI: function(message) {
            var self = this;
            fetch(this.config.apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    action: 'send_message',
                    message: message
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.response) {
                    self.addMessage(data.response, 'bot');
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        }
    };
})();
JS;
    }

    /**
     * Find embed configuration by token.
     */
    private function findEmbedConfig($token)
    {
        $channels = Channel::where('type', 'webchat')->get();
        
        foreach ($channels as $channel) {
            $config = $channel->config;
            if (isset($config['embed_configs'][$token])) {
                return $config['embed_configs'][$token];
            }
        }
        
        return null;
    }

    /**
     * Handle widget initialization.
     */
    private function handleInit($embedConfig)
    {
        return response()->json([
            'success' => true,
            'config' => $embedConfig,
            'online' => true
        ]);
    }

    /**
     * Handle sending a message.
     */
    private function handleSendMessage(Request $request, $embedConfig)
    {
        $message = $request->input('message');
        
        // Here you would typically:
        // 1. Create a new chat/conversation
        // 2. Store the message
        // 3. Trigger chatbot or route to agent
        // 4. Return appropriate response
        
        // For now, return a simple response
        return response()->json([
            'success' => true,
            'response' => 'Thank you for your message. An agent will be with you shortly.'
        ]);
    }

    /**
     * Handle getting messages.
     */
    private function handleGetMessages(Request $request, $embedConfig)
    {
        // Here you would fetch conversation history
        return response()->json([
            'success' => true,
            'messages' => []
        ]);
    }

    /**
     * Convert boolean to JavaScript boolean string.
     */
    private function boolToJs($bool): string
    {
        return $bool ? 'true' : 'false';
    }
}
