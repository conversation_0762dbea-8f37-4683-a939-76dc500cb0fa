<?php

namespace App\Http\Controllers;

use App\Models\Template;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class TemplateController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $query = $user->company->templates();

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Search by name or content
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }

        $templates = $query->latest()->paginate(20);

        $templateTypes = [
            'whatsapp' => 'WhatsApp',
            'sms' => 'SMS',
            'email' => 'Email',
            'webchat' => 'Web Chat'
        ];

        return view('templates.index', compact('templates', 'templateTypes'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $templateTypes = [
            'whatsapp' => 'WhatsApp',
            'sms' => 'SMS',
            'email' => 'Email',
            'webchat' => 'Web Chat'
        ];

        return view('templates.create', compact('templateTypes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|in:whatsapp,sms,email,webchat',
            'content' => 'required|string',
            'variables' => 'nullable|array',
        ]);

        $user = Auth::user();

        $template = Template::create([
            'company_id' => $user->company_id,
            'name' => $request->name,
            'type' => $request->type,
            'content' => $request->content,
            'variables' => $request->variables ?? [],
            'status' => 'draft',
            'is_active' => false,
        ]);

        return redirect()->route('templates.index')
            ->with('success', 'Template created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Template $template)
    {
        $this->authorize('view', $template);

        return view('templates.show', compact('template'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Template $template)
    {
        $this->authorize('update', $template);

        $templateTypes = [
            'whatsapp' => 'WhatsApp',
            'sms' => 'SMS',
            'email' => 'Email',
            'webchat' => 'Web Chat'
        ];

        return view('templates.edit', compact('template', 'templateTypes'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Template $template)
    {
        $this->authorize('update', $template);

        $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|in:whatsapp,sms,email,webchat',
            'content' => 'required|string',
            'variables' => 'nullable|array',
        ]);

        $template->update([
            'name' => $request->name,
            'type' => $request->type,
            'content' => $request->content,
            'variables' => $request->variables ?? [],
        ]);

        return redirect()->route('templates.index')
            ->with('success', 'Template updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Template $template)
    {
        $this->authorize('delete', $template);

        $template->delete();

        return redirect()->route('templates.index')
            ->with('success', 'Template deleted successfully!');
    }

    /**
     * Duplicate a template.
     */
    public function duplicate(Template $template)
    {
        $this->authorize('view', $template);

        $newTemplate = Template::create([
            'company_id' => $template->company_id,
            'name' => $template->name . ' (Copy)',
            'type' => $template->type,
            'content' => $template->content,
            'variables' => $template->variables,
            'status' => 'draft',
            'is_active' => false,
        ]);

        return redirect()->route('templates.edit', $newTemplate)
            ->with('success', 'Template duplicated successfully!');
    }

    /**
     * Approve a template.
     */
    public function approve(Template $template)
    {
        $this->authorize('update', $template);

        $template->update([
            'status' => 'approved',
            'is_active' => true,
        ]);

        return redirect()->route('templates.index')
            ->with('success', 'Template approved successfully!');
    }

    /**
     * Preview template with variables.
     */
    public function preview(Request $request, Template $template)
    {
        $this->authorize('view', $template);

        $content = $template->content;
        $variables = $request->input('variables', []);

        // Replace variables in content
        foreach ($variables as $key => $value) {
            $content = str_replace('{{' . $key . '}}', $value, $content);
        }

        return response()->json([
            'content' => $content,
            'variables' => $template->variables
        ]);
    }

    /**
     * Get template variables from content.
     */
    public function extractVariables(Request $request)
    {
        $content = $request->input('content', '');
        
        // Extract variables like {{variable_name}}
        preg_match_all('/\{\{([^}]+)\}\}/', $content, $matches);
        
        $variables = array_unique($matches[1]);
        
        return response()->json(['variables' => $variables]);
    }
}
