<?php

namespace App\Http\Controllers;

use App\Models\Message;
use App\Models\Channel;
use App\Models\Template;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MessageController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of messages.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $query = $user->company->messages()->with(['channel', 'chat']);

        // Filter by channel
        if ($request->filled('channel_id')) {
            $query->where('channel_id', $request->channel_id);
        }

        // Filter by direction
        if ($request->filled('direction')) {
            $query->where('direction', $request->direction);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Search by content or recipient
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('content', 'like', "%{$search}%")
                  ->orWhere('recipient', 'like', "%{$search}%");
            });
        }

        $messages = $query->latest()->paginate(20);
        $channels = $user->company->channels()->get();

        return view('messages.index', compact('messages', 'channels'));
    }

    /**
     * Show the form for creating a new message.
     */
    public function create()
    {
        $user = Auth::user();
        $channels = $user->company->channels()->where('is_enabled', true)->get();
        $templates = $user->company->templates()->where('is_active', true)->get();

        return view('messages.create', compact('channels', 'templates'));
    }

    /**
     * Store a newly created message.
     */
    public function store(Request $request)
    {
        $request->validate([
            'channel_id' => 'required|exists:channels,id',
            'recipient' => 'required|string|max:255',
            'content' => 'required|string',
            'type' => 'required|in:text,image,document,audio,video',
            'scheduled_at' => 'nullable|date|after:now',
        ]);

        $user = Auth::user();

        // Verify channel belongs to user's company
        $channel = Channel::where('id', $request->channel_id)
            ->where('company_id', $user->company_id)
            ->firstOrFail();

        $message = Message::create([
            'company_id' => $user->company_id,
            'channel_id' => $request->channel_id,
            'recipient' => $request->recipient,
            'content' => $request->content,
            'type' => $request->type,
            'direction' => 'outbound',
            'status' => $request->scheduled_at ? 'scheduled' : 'queued',
            'scheduled_at' => $request->scheduled_at,
        ]);

        // If not scheduled, attempt to send immediately
        if (!$request->scheduled_at) {
            $this->sendMessage($message);
        }

        return redirect()->route('messages.index')
            ->with('success', 'Message created successfully!');
    }

    /**
     * Display the specified message.
     */
    public function show(Message $message)
    {
        $this->authorize('view', $message);

        $message->load(['channel', 'chat']);

        return view('messages.show', compact('message'));
    }

    /**
     * Update the specified message.
     */
    public function update(Request $request, Message $message)
    {
        $this->authorize('update', $message);

        // Only allow updating scheduled messages
        if ($message->status !== 'scheduled') {
            return back()->withErrors(['message' => 'Only scheduled messages can be updated.']);
        }

        $request->validate([
            'content' => 'required|string',
            'scheduled_at' => 'nullable|date|after:now',
        ]);

        $message->update([
            'content' => $request->content,
            'scheduled_at' => $request->scheduled_at,
        ]);

        return redirect()->route('messages.index')
            ->with('success', 'Message updated successfully!');
    }

    /**
     * Remove the specified message.
     */
    public function destroy(Message $message)
    {
        $this->authorize('delete', $message);

        // Only allow deleting scheduled or failed messages
        if (!in_array($message->status, ['scheduled', 'failed'])) {
            return back()->withErrors(['message' => 'Only scheduled or failed messages can be deleted.']);
        }

        $message->delete();

        return redirect()->route('messages.index')
            ->with('success', 'Message deleted successfully!');
    }

    /**
     * Send a message immediately.
     */
    public function send(Request $request)
    {
        $request->validate([
            'channel_id' => 'required|exists:channels,id',
            'recipient' => 'required|string|max:255',
            'content' => 'required|string',
            'type' => 'required|in:text,image,document,audio,video',
            'template_id' => 'nullable|exists:templates,id',
            'template_variables' => 'nullable|array',
        ]);

        $user = Auth::user();

        // Verify channel belongs to user's company
        $channel = Channel::where('id', $request->channel_id)
            ->where('company_id', $user->company_id)
            ->firstOrFail();

        $content = $request->content;

        // If using a template, process variables
        if ($request->template_id) {
            $template = Template::where('id', $request->template_id)
                ->where('company_id', $user->company_id)
                ->firstOrFail();

            $content = $this->processTemplate($template, $request->template_variables ?? []);
        }

        $message = Message::create([
            'company_id' => $user->company_id,
            'channel_id' => $request->channel_id,
            'recipient' => $request->recipient,
            'content' => $content,
            'type' => $request->type,
            'direction' => 'outbound',
            'status' => 'queued',
        ]);

        // Attempt to send immediately
        $sent = $this->sendMessage($message);

        if ($sent) {
            return response()->json([
                'success' => true,
                'message' => 'Message sent successfully!',
                'message_id' => $message->id
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send message. It has been queued for retry.'
            ], 500);
        }
    }

    /**
     * Get conversation history with a contact.
     */
    public function conversation(Request $request, $contact)
    {
        $user = Auth::user();
        
        $messages = $user->company->messages()
            ->where('recipient', $contact)
            ->orWhere(function($query) use ($contact) {
                $query->where('direction', 'inbound')
                      ->where('sender', $contact);
            })
            ->with('channel')
            ->orderBy('created_at')
            ->get();

        return view('messages.conversation', compact('messages', 'contact'));
    }

    /**
     * Get message statistics.
     */
    public function stats(Request $request)
    {
        $user = Auth::user();
        $query = $user->company->messages();

        // Apply date filter if provided
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $stats = [
            'total' => $query->count(),
            'sent' => $query->where('status', 'sent')->count(),
            'delivered' => $query->where('status', 'delivered')->count(),
            'failed' => $query->where('status', 'failed')->count(),
            'queued' => $query->where('status', 'queued')->count(),
            'scheduled' => $query->where('status', 'scheduled')->count(),
        ];

        // Calculate delivery rate
        $stats['delivery_rate'] = $stats['sent'] > 0 
            ? round(($stats['delivered'] / $stats['sent']) * 100, 2) 
            : 0;

        return response()->json($stats);
    }

    /**
     * Process template with variables.
     */
    private function processTemplate(Template $template, array $variables): string
    {
        $content = $template->content;

        foreach ($variables as $key => $value) {
            $content = str_replace('{{' . $key . '}}', $value, $content);
        }

        return $content;
    }

    /**
     * Send a message through the appropriate channel.
     */
    private function sendMessage(Message $message): bool
    {
        try {
            $channel = $message->channel;

            switch ($channel->type) {
                case 'whatsapp':
                    return $this->sendWhatsAppMessage($message);
                case 'sms':
                    return $this->sendSMSMessage($message);
                case 'email':
                    return $this->sendEmailMessage($message);
                case 'telegram':
                    return $this->sendTelegramMessage($message);
                case 'webchat':
                    return $this->sendWebChatMessage($message);
                default:
                    throw new \Exception('Unsupported channel type: ' . $channel->type);
            }
        } catch (\Exception $e) {
            $message->update([
                'status' => 'failed',
                'error_message' => $e->getMessage(),
                'failed_at' => now(),
            ]);

            return false;
        }
    }

    /**
     * Send WhatsApp message.
     */
    private function sendWhatsAppMessage(Message $message): bool
    {
        // Implement WhatsApp API integration
        // This is a placeholder implementation
        
        $message->update([
            'status' => 'sent',
            'sent_at' => now(),
        ]);

        return true;
    }

    /**
     * Send SMS message.
     */
    private function sendSMSMessage(Message $message): bool
    {
        // Implement SMS gateway integration
        // This is a placeholder implementation
        
        $message->update([
            'status' => 'sent',
            'sent_at' => now(),
        ]);

        return true;
    }

    /**
     * Send email message.
     */
    private function sendEmailMessage(Message $message): bool
    {
        // Implement email sending
        // This is a placeholder implementation
        
        $message->update([
            'status' => 'sent',
            'sent_at' => now(),
        ]);

        return true;
    }

    /**
     * Send Telegram message.
     */
    private function sendTelegramMessage(Message $message): bool
    {
        // Implement Telegram Bot API integration
        // This is a placeholder implementation
        
        $message->update([
            'status' => 'sent',
            'sent_at' => now(),
        ]);

        return true;
    }

    /**
     * Send web chat message.
     */
    private function sendWebChatMessage(Message $message): bool
    {
        // For web chat, the message is typically stored and delivered via WebSocket
        // This is a placeholder implementation
        
        $message->update([
            'status' => 'sent',
            'sent_at' => now(),
        ]);

        return true;
    }
}
