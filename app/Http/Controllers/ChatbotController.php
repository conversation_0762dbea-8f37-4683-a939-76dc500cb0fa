<?php

namespace App\Http\Controllers;

use App\Models\Chatbot;
use App\Models\Channel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ChatbotController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $query = $user->company->chatbots()->with('channel');

        // Filter by channel
        if ($request->filled('channel_id')) {
            $query->where('channel_id', $request->channel_id);
        }

        // Filter by status
        if ($request->filled('status')) {
            $isActive = $request->status === 'active';
            $query->where('is_active', $isActive);
        }

        // Search by name or description
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $chatbots = $query->latest()->paginate(20);
        $channels = $user->company->channels()->get();

        return view('chatbots.index', compact('chatbots', 'channels'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $user = Auth::user();
        $channels = $user->company->channels()->where('is_enabled', true)->get();

        return view('chatbots.create', compact('channels'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'channel_id' => 'required|exists:channels,id',
            'flow_data' => 'nullable|array',
            'settings' => 'nullable|array',
        ]);

        $user = Auth::user();

        // Verify channel belongs to user's company
        $channel = Channel::where('id', $request->channel_id)
            ->where('company_id', $user->company_id)
            ->firstOrFail();

        $chatbot = Chatbot::create([
            'company_id' => $user->company_id,
            'channel_id' => $request->channel_id,
            'name' => $request->name,
            'description' => $request->description,
            'flow_data' => $request->flow_data ?? $this->getDefaultFlow(),
            'settings' => $request->settings ?? $this->getDefaultSettings(),
            'is_active' => false,
            'conversions' => 0,
        ]);

        return redirect()->route('chatbots.index')
            ->with('success', 'Chatbot created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Chatbot $chatbot)
    {
        $this->authorize('view', $chatbot);

        $chatbot->load('channel');

        // Get analytics data
        $analytics = $this->getChatbotAnalytics($chatbot);

        return view('chatbots.show', compact('chatbot', 'analytics'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Chatbot $chatbot)
    {
        $this->authorize('update', $chatbot);

        $user = Auth::user();
        $channels = $user->company->channels()->where('is_enabled', true)->get();

        return view('chatbots.edit', compact('chatbot', 'channels'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Chatbot $chatbot)
    {
        $this->authorize('update', $chatbot);

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'channel_id' => 'required|exists:channels,id',
            'flow_data' => 'nullable|array',
            'settings' => 'nullable|array',
        ]);

        // Verify channel belongs to user's company
        $channel = Channel::where('id', $request->channel_id)
            ->where('company_id', $chatbot->company_id)
            ->firstOrFail();

        $chatbot->update([
            'channel_id' => $request->channel_id,
            'name' => $request->name,
            'description' => $request->description,
            'flow_data' => $request->flow_data ?? $chatbot->flow_data,
            'settings' => $request->settings ?? $chatbot->settings,
        ]);

        return redirect()->route('chatbots.index')
            ->with('success', 'Chatbot updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Chatbot $chatbot)
    {
        $this->authorize('delete', $chatbot);

        $chatbot->delete();

        return redirect()->route('chatbots.index')
            ->with('success', 'Chatbot deleted successfully!');
    }

    /**
     * Toggle chatbot active status.
     */
    public function toggle(Chatbot $chatbot)
    {
        $this->authorize('update', $chatbot);

        $chatbot->update([
            'is_active' => !$chatbot->is_active
        ]);

        $status = $chatbot->is_active ? 'activated' : 'deactivated';

        return redirect()->route('chatbots.index')
            ->with('success', "Chatbot {$status} successfully!");
    }

    /**
     * Test chatbot flow.
     */
    public function test(Request $request, Chatbot $chatbot)
    {
        $this->authorize('view', $chatbot);

        $message = $request->input('message', 'Hello');
        
        // Simulate chatbot response
        $response = $this->simulateChatbotResponse($chatbot, $message);

        return response()->json([
            'success' => true,
            'response' => $response,
            'flow_data' => $chatbot->flow_data
        ]);
    }

    /**
     * Get chatbot builder interface.
     */
    public function builder(Chatbot $chatbot)
    {
        $this->authorize('update', $chatbot);

        return view('chatbots.builder', compact('chatbot'));
    }

    /**
     * Save chatbot flow.
     */
    public function saveFlow(Request $request, Chatbot $chatbot)
    {
        $this->authorize('update', $chatbot);

        $request->validate([
            'flow_data' => 'required|array',
        ]);

        $chatbot->update([
            'flow_data' => $request->flow_data
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Chatbot flow saved successfully!'
        ]);
    }

    /**
     * Get default chatbot flow.
     */
    private function getDefaultFlow(): array
    {
        return [
            'nodes' => [
                [
                    'id' => 'start',
                    'type' => 'start',
                    'position' => ['x' => 100, 'y' => 100],
                    'data' => [
                        'label' => 'Start',
                        'message' => 'Hello! How can I help you today?'
                    ]
                ]
            ],
            'edges' => []
        ];
    }

    /**
     * Get default chatbot settings.
     */
    private function getDefaultSettings(): array
    {
        return [
            'welcome_message' => 'Hello! How can I help you today?',
            'fallback_message' => 'I didn\'t understand that. Can you please rephrase?',
            'handover_message' => 'Let me connect you with a human agent.',
            'timeout' => 300, // 5 minutes
            'max_retries' => 3,
        ];
    }

    /**
     * Get chatbot analytics.
     */
    private function getChatbotAnalytics(Chatbot $chatbot): array
    {
        // This would typically query actual conversation data
        return [
            'total_conversations' => rand(50, 500),
            'completed_flows' => rand(30, 400),
            'handovers' => rand(5, 50),
            'conversion_rate' => rand(60, 95) . '%',
            'avg_session_duration' => rand(2, 10) . ' minutes',
        ];
    }

    /**
     * Simulate chatbot response for testing.
     */
    private function simulateChatbotResponse(Chatbot $chatbot, string $message): array
    {
        $flowData = $chatbot->flow_data;
        
        // Simple simulation - in real implementation, this would process the flow
        return [
            'message' => $chatbot->settings['welcome_message'] ?? 'Hello! How can I help you?',
            'type' => 'text',
            'quick_replies' => ['Help', 'Contact Support', 'More Info']
        ];
    }
}
