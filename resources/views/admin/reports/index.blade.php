@extends('layouts.admin')

@section('page-title', 'Reports & Analytics')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">Reports & Analytics</h2>
                <p class="text-gray-600">View system analytics, usage reports, and export data</p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{{ route('admin.reports.analytics') }}" 
                   class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-chart-line mr-2"></i>
                    Detailed Analytics
                </a>
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open"
                            class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-download mr-2"></i>
                        Export Data
                        <i class="fas fa-chevron-down ml-2"></i>
                    </button>
                    <div x-show="open" @click.away="open = false" x-transition
                         class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                        <div class="py-1">
                            <a href="{{ route('admin.reports.export', 'users') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                <i class="fas fa-users mr-2"></i>
                                Export Users
                            </a>
                            <a href="{{ route('admin.reports.export', 'clients') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                <i class="fas fa-building mr-2"></i>
                                Export Clients
                            </a>
                            <a href="{{ route('admin.reports.export', 'messages') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                <i class="fas fa-comments mr-2"></i>
                                Export Messages
                            </a>
                            <a href="{{ route('admin.reports.export', 'analytics') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                <i class="fas fa-chart-bar mr-2"></i>
                                Export Analytics
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Overview Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Users</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $overview['total_users']['count'] }}</p>
                    <p class="text-sm {{ $overview['total_users']['change'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                        {{ $overview['total_users']['change'] >= 0 ? '+' : '' }}{{ $overview['total_users']['change'] }}% from last week
                    </p>
                </div>
                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-users text-blue-600 text-xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Companies</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $overview['total_companies']['count'] }}</p>
                    <p class="text-sm {{ $overview['total_companies']['change'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                        {{ $overview['total_companies']['change'] >= 0 ? '+' : '' }}{{ $overview['total_companies']['change'] }}% from last week
                    </p>
                </div>
                <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-building text-green-600 text-xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Messages</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $overview['total_messages']['count'] }}</p>
                    <p class="text-sm {{ $overview['total_messages']['change'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                        {{ $overview['total_messages']['change'] >= 0 ? '+' : '' }}{{ $overview['total_messages']['change'] }}% from yesterday
                    </p>
                </div>
                <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-comments text-purple-600 text-xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Active Channels</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $overview['active_channels']['count'] }}</p>
                    <p class="text-sm text-gray-500">All channels operational</p>
                </div>
                <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-broadcast-tower text-yellow-600 text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Top Clients -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Clients by Message Volume</h3>
            <div class="space-y-4">
                @forelse($topClients->take(5) as $client)
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <span class="text-blue-600 font-medium text-sm">{{ substr($client->name, 0, 1) }}</span>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-900">{{ $client->name }}</p>
                            <p class="text-xs text-gray-500">{{ $client->email }}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-sm font-medium text-gray-900">{{ $client->messages_count }}</p>
                        <p class="text-xs text-gray-500">messages</p>
                    </div>
                </div>
                @empty
                <p class="text-gray-500 text-center py-4">No client data available</p>
                @endforelse
            </div>
        </div>

        <!-- Channel Statistics -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Channel Performance</h3>
            <div class="space-y-4">
                @forelse($channelStats as $type => $stats)
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <i class="fas {{ $type === 'whatsapp' ? 'fa-whatsapp' : ($type === 'sms' ? 'fa-sms' : ($type === 'email' ? 'fa-envelope' : 'fa-comments')) }} text-green-600 text-sm"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-900">{{ ucfirst($type) }}</p>
                            <p class="text-xs text-gray-500">{{ $stats['enabled'] }}/{{ $stats['count'] }} active</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-sm font-medium text-gray-900">{{ $stats['total_messages'] }}</p>
                        <p class="text-xs text-gray-500">messages</p>
                    </div>
                </div>
                @empty
                <p class="text-gray-500 text-center py-4">No channel data available</p>
                @endforelse
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- Recent Users -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Users</h3>
            <div class="space-y-3">
                @forelse($recentActivity['recent_users'] as $user)
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <span class="text-blue-600 font-medium text-sm">{{ substr($user->name, 0, 1) }}</span>
                    </div>
                    <div class="ml-3 flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate">{{ $user->name }}</p>
                        <p class="text-xs text-gray-500">{{ $user->created_at->diffForHumans() }}</p>
                    </div>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $user->role === 'admin' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800' }}">
                        {{ ucfirst($user->role) }}
                    </span>
                </div>
                @empty
                <p class="text-gray-500 text-center py-4">No recent users</p>
                @endforelse
            </div>
        </div>

        <!-- Recent Companies -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Companies</h3>
            <div class="space-y-3">
                @forelse($recentActivity['recent_companies'] as $company)
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <span class="text-green-600 font-medium text-sm">{{ substr($company->name, 0, 1) }}</span>
                    </div>
                    <div class="ml-3 flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate">{{ $company->name }}</p>
                        <p class="text-xs text-gray-500">{{ $company->created_at->diffForHumans() }}</p>
                    </div>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $company->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                        {{ $company->is_active ? 'Active' : 'Inactive' }}
                    </span>
                </div>
                @empty
                <p class="text-gray-500 text-center py-4">No recent companies</p>
                @endforelse
            </div>
        </div>

        <!-- Recent Messages -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Messages</h3>
            <div class="space-y-3">
                @forelse($recentActivity['recent_messages'] as $message)
                <div class="flex items-start">
                    <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-comment text-purple-600 text-sm"></i>
                    </div>
                    <div class="ml-3 flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate">
                            {{ $message->company ? $message->company->name : 'Unknown' }}
                        </p>
                        <p class="text-xs text-gray-500 truncate">
                            {{ Str::limit($message->content, 50) }}
                        </p>
                        <p class="text-xs text-gray-400">{{ $message->created_at->diffForHumans() }}</p>
                    </div>
                </div>
                @empty
                <p class="text-gray-500 text-center py-4">No recent messages</p>
                @endforelse
            </div>
        </div>
    </div>

    <!-- Quick Reports -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Reports</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <a href="{{ route('admin.reports.export', 'users') }}?period=30days" 
               class="flex items-center justify-center px-4 py-3 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors">
                <div class="text-center">
                    <i class="fas fa-users text-blue-600 text-xl mb-2"></i>
                    <p class="text-sm font-medium text-blue-900">User Report</p>
                    <p class="text-xs text-blue-700">Last 30 days</p>
                </div>
            </a>

            <a href="{{ route('admin.reports.export', 'clients') }}?period=30days" 
               class="flex items-center justify-center px-4 py-3 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors">
                <div class="text-center">
                    <i class="fas fa-building text-green-600 text-xl mb-2"></i>
                    <p class="text-sm font-medium text-green-900">Client Report</p>
                    <p class="text-xs text-green-700">Last 30 days</p>
                </div>
            </a>

            <a href="{{ route('admin.reports.export', 'messages') }}?period=7days" 
               class="flex items-center justify-center px-4 py-3 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors">
                <div class="text-center">
                    <i class="fas fa-comments text-purple-600 text-xl mb-2"></i>
                    <p class="text-sm font-medium text-purple-900">Message Report</p>
                    <p class="text-xs text-purple-700">Last 7 days</p>
                </div>
            </a>

            <a href="{{ route('admin.reports.analytics') }}" 
               class="flex items-center justify-center px-4 py-3 bg-yellow-50 border border-yellow-200 rounded-lg hover:bg-yellow-100 transition-colors">
                <div class="text-center">
                    <i class="fas fa-chart-line text-yellow-600 text-xl mb-2"></i>
                    <p class="text-sm font-medium text-yellow-900">Analytics</p>
                    <p class="text-xs text-yellow-700">Detailed view</p>
                </div>
            </a>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Auto-refresh data every 5 minutes
setInterval(function() {
    location.reload();
}, 300000);
</script>
@endpush
@endsection
