@extends('layouts.admin')

@section('page-title', 'System Settings')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">System Settings</h2>
                <p class="text-gray-600">Configure application settings, email, API, and system preferences</p>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="clearCache()" 
                        class="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors">
                    <i class="fas fa-broom mr-2"></i>
                    Clear Cache
                </button>
                <button onclick="exportSettings()" 
                        class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-download mr-2"></i>
                    Export Settings
                </button>
            </div>
        </div>
    </div>

    <form action="{{ route('admin.settings.update') }}" method="POST" x-data="settingsForm()">
        @csrf
        @method('PUT')

        <!-- General Settings -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">General Settings</h3>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label for="app_name" class="block text-sm font-medium text-gray-700 mb-2">
                        Application Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="app_name" name="app_name" required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 @error('app_name') border-red-500 @enderror"
                           value="{{ old('app_name', $settings['app_name']) }}">
                    @error('app_name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="app_url" class="block text-sm font-medium text-gray-700 mb-2">
                        Application URL <span class="text-red-500">*</span>
                    </label>
                    <input type="url" id="app_url" name="app_url" required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 @error('app_url') border-red-500 @enderror"
                           value="{{ old('app_url', $settings['app_url']) }}">
                    @error('app_url')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="app_timezone" class="block text-sm font-medium text-gray-700 mb-2">
                        Timezone <span class="text-red-500">*</span>
                    </label>
                    <select id="app_timezone" name="app_timezone" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="UTC" {{ old('app_timezone', $settings['app_timezone']) === 'UTC' ? 'selected' : '' }}>UTC</option>
                        <option value="America/New_York" {{ old('app_timezone', $settings['app_timezone']) === 'America/New_York' ? 'selected' : '' }}>Eastern Time</option>
                        <option value="America/Chicago" {{ old('app_timezone', $settings['app_timezone']) === 'America/Chicago' ? 'selected' : '' }}>Central Time</option>
                        <option value="America/Denver" {{ old('app_timezone', $settings['app_timezone']) === 'America/Denver' ? 'selected' : '' }}>Mountain Time</option>
                        <option value="America/Los_Angeles" {{ old('app_timezone', $settings['app_timezone']) === 'America/Los_Angeles' ? 'selected' : '' }}>Pacific Time</option>
                    </select>
                </div>

                <div>
                    <label for="app_locale" class="block text-sm font-medium text-gray-700 mb-2">
                        Language <span class="text-red-500">*</span>
                    </label>
                    <select id="app_locale" name="app_locale" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="en" {{ old('app_locale', $settings['app_locale']) === 'en' ? 'selected' : '' }}>English</option>
                        <option value="es" {{ old('app_locale', $settings['app_locale']) === 'es' ? 'selected' : '' }}>Spanish</option>
                        <option value="fr" {{ old('app_locale', $settings['app_locale']) === 'fr' ? 'selected' : '' }}>French</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Email Settings -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Email Settings</h3>
                <button type="button" onclick="testEmail()" 
                        class="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700">
                    Test Email
                </button>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label for="mail_driver" class="block text-sm font-medium text-gray-700 mb-2">
                        Mail Driver <span class="text-red-500">*</span>
                    </label>
                    <select id="mail_driver" name="mail_driver" required x-model="mailDriver"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="smtp" {{ old('mail_driver', $settings['mail_driver']) === 'smtp' ? 'selected' : '' }}>SMTP</option>
                        <option value="sendmail" {{ old('mail_driver', $settings['mail_driver']) === 'sendmail' ? 'selected' : '' }}>Sendmail</option>
                        <option value="mailgun" {{ old('mail_driver', $settings['mail_driver']) === 'mailgun' ? 'selected' : '' }}>Mailgun</option>
                    </select>
                </div>

                <div x-show="mailDriver === 'smtp'">
                    <label for="mail_host" class="block text-sm font-medium text-gray-700 mb-2">
                        SMTP Host <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="mail_host" name="mail_host"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                           value="{{ old('mail_host', $settings['mail_host']) }}">
                </div>

                <div x-show="mailDriver === 'smtp'">
                    <label for="mail_port" class="block text-sm font-medium text-gray-700 mb-2">
                        SMTP Port <span class="text-red-500">*</span>
                    </label>
                    <input type="number" id="mail_port" name="mail_port"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                           value="{{ old('mail_port', $settings['mail_port']) }}">
                </div>

                <div x-show="mailDriver === 'smtp'">
                    <label for="mail_username" class="block text-sm font-medium text-gray-700 mb-2">
                        SMTP Username <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="mail_username" name="mail_username"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                           value="{{ old('mail_username', $settings['mail_username']) }}">
                </div>

                <div x-show="mailDriver === 'smtp'">
                    <label for="mail_password" class="block text-sm font-medium text-gray-700 mb-2">
                        SMTP Password
                    </label>
                    <input type="password" id="mail_password" name="mail_password"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="Leave blank to keep current password">
                </div>

                <div x-show="mailDriver === 'smtp'">
                    <label for="mail_encryption" class="block text-sm font-medium text-gray-700 mb-2">
                        Encryption
                    </label>
                    <select id="mail_encryption" name="mail_encryption"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">None</option>
                        <option value="tls" {{ old('mail_encryption', $settings['mail_encryption']) === 'tls' ? 'selected' : '' }}>TLS</option>
                        <option value="ssl" {{ old('mail_encryption', $settings['mail_encryption']) === 'ssl' ? 'selected' : '' }}>SSL</option>
                    </select>
                </div>

                <div>
                    <label for="mail_from_address" class="block text-sm font-medium text-gray-700 mb-2">
                        From Email <span class="text-red-500">*</span>
                    </label>
                    <input type="email" id="mail_from_address" name="mail_from_address" required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                           value="{{ old('mail_from_address', $settings['mail_from_address']) }}">
                </div>

                <div>
                    <label for="mail_from_name" class="block text-sm font-medium text-gray-700 mb-2">
                        From Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="mail_from_name" name="mail_from_name" required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                           value="{{ old('mail_from_name', $settings['mail_from_name']) }}">
                </div>
            </div>
        </div>

        <!-- Security Settings -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Security Settings</h3>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label for="session_lifetime" class="block text-sm font-medium text-gray-700 mb-2">
                        Session Lifetime (minutes) <span class="text-red-500">*</span>
                    </label>
                    <input type="number" id="session_lifetime" name="session_lifetime" required min="1" max="10080"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                           value="{{ old('session_lifetime', $settings['session_lifetime']) }}">
                </div>

                <div>
                    <label for="password_min_length" class="block text-sm font-medium text-gray-700 mb-2">
                        Minimum Password Length <span class="text-red-500">*</span>
                    </label>
                    <input type="number" id="password_min_length" name="password_min_length" required min="6" max="50"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                           value="{{ old('password_min_length', $settings['password_min_length']) }}">
                </div>

                <div>
                    <label for="max_login_attempts" class="block text-sm font-medium text-gray-700 mb-2">
                        Max Login Attempts <span class="text-red-500">*</span>
                    </label>
                    <input type="number" id="max_login_attempts" name="max_login_attempts" required min="1" max="20"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                           value="{{ old('max_login_attempts', $settings['max_login_attempts']) }}">
                </div>

                <div>
                    <label for="lockout_duration" class="block text-sm font-medium text-gray-700 mb-2">
                        Lockout Duration (minutes) <span class="text-red-500">*</span>
                    </label>
                    <input type="number" id="lockout_duration" name="lockout_duration" required min="1" max="1440"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                           value="{{ old('lockout_duration', $settings['lockout_duration']) }}">
                </div>
            </div>
        </div>

        <!-- File Upload Settings -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">File Upload Settings</h3>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label for="max_file_size" class="block text-sm font-medium text-gray-700 mb-2">
                        Max File Size (MB) <span class="text-red-500">*</span>
                    </label>
                    <input type="number" id="max_file_size" name="max_file_size" required min="1" max="100"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                           value="{{ old('max_file_size', $settings['max_file_size']) }}">
                </div>

                <div>
                    <label for="allowed_file_types" class="block text-sm font-medium text-gray-700 mb-2">
                        Allowed File Types <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="allowed_file_types" name="allowed_file_types" required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                           value="{{ old('allowed_file_types', $settings['allowed_file_types']) }}"
                           placeholder="jpg,jpeg,png,gif,pdf,doc,docx">
                    <p class="mt-1 text-xs text-gray-500">Separate file extensions with commas</p>
                </div>
            </div>
        </div>

        <!-- Notification Settings -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Notification Settings</h3>
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="enable_email_notifications" value="1" 
                               {{ old('enable_email_notifications', $settings['enable_email_notifications']) ? 'checked' : '' }}
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-700">Enable Email Notifications</span>
                    </label>
                </div>

                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="enable_sms_notifications" value="1"
                               {{ old('enable_sms_notifications', $settings['enable_sms_notifications']) ? 'checked' : '' }}
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-700">Enable SMS Notifications</span>
                    </label>
                </div>

                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="enable_push_notifications" value="1"
                               {{ old('enable_push_notifications', $settings['enable_push_notifications']) ? 'checked' : '' }}
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-700">Enable Push Notifications</span>
                    </label>
                </div>
            </div>
        </div>

        <!-- Maintenance Settings -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Maintenance Settings</h3>
            
            <div class="space-y-4">
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="maintenance_mode" value="1"
                               {{ old('maintenance_mode', $settings['maintenance_mode']) ? 'checked' : '' }}
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-700">Enable Maintenance Mode</span>
                    </label>
                    <p class="mt-1 text-xs text-gray-500">When enabled, only admins can access the application</p>
                </div>

                <div>
                    <label for="maintenance_message" class="block text-sm font-medium text-gray-700 mb-2">
                        Maintenance Message
                    </label>
                    <textarea id="maintenance_message" name="maintenance_message" rows="3"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">{{ old('maintenance_message', $settings['maintenance_message']) }}</textarea>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-between pt-6 border-t border-gray-200">
            <div class="flex items-center space-x-4">
                <button type="button" onclick="resetSettings()" 
                        class="px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                    Reset to Defaults
                </button>
            </div>
            <div class="flex items-center space-x-3">
                <button type="submit" 
                        class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-save mr-2"></i>
                    Save Settings
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Test Email Modal -->
<div id="testEmailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Test Email Configuration</h3>
                <div class="mb-4">
                    <label for="test_email" class="block text-sm font-medium text-gray-700 mb-2">
                        Test Email Address
                    </label>
                    <input type="email" id="test_email" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="Enter email address">
                </div>
            </div>
            
            <div class="px-6 py-4 bg-gray-50 rounded-b-lg flex justify-end space-x-3">
                <button type="button" onclick="closeTestEmailModal()"
                        class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors">
                    Cancel
                </button>
                <button type="button" onclick="sendTestEmail()"
                        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    Send Test Email
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function settingsForm() {
    return {
        mailDriver: '{{ old("mail_driver", $settings["mail_driver"]) }}'
    }
}

function testEmail() {
    document.getElementById('testEmailModal').classList.remove('hidden');
}

function closeTestEmailModal() {
    document.getElementById('testEmailModal').classList.add('hidden');
}

function sendTestEmail() {
    const email = document.getElementById('test_email').value;
    if (!email) {
        alert('Please enter an email address');
        return;
    }

    fetch('{{ route("admin.settings.test-email") }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}',
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ test_email: email })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Test email sent successfully!');
            closeTestEmailModal();
        } else {
            alert('Failed to send test email: ' + data.message);
        }
    })
    .catch(error => {
        alert('Error: ' + error.message);
    });
}

function clearCache() {
    if (confirm('Are you sure you want to clear the application cache?')) {
        fetch('/admin/settings/clear-cache', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Cache cleared successfully!');
            } else {
                alert('Failed to clear cache: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error: ' + error.message);
        });
    }
}

function exportSettings() {
    window.location.href = '{{ route("admin.settings.export") }}';
}

function resetSettings() {
    if (confirm('Are you sure you want to reset all settings to default values?')) {
        window.location.href = '{{ route("admin.settings.reset") }}';
    }
}
</script>
@endpush
@endsection
