@extends('layouts.admin')

@section('page-title', 'System Monitoring')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="mb-6">
        <h2 class="text-2xl font-bold text-gray-900">System Monitoring</h2>
        <p class="text-gray-600">Monitor system health, performance metrics, and real-time statistics</p>
    </div>

    <!-- System Health Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Overall Status</p>
                    <p class="text-2xl font-bold {{ $systemHealth['overall_status'] === 'healthy' ? 'text-green-600' : ($systemHealth['overall_status'] === 'warning' ? 'text-yellow-600' : 'text-red-600') }}">
                        {{ ucfirst($systemHealth['overall_status']) }}
                    </p>
                </div>
                <div class="w-12 h-12 {{ $systemHealth['overall_status'] === 'healthy' ? 'bg-green-100' : ($systemHealth['overall_status'] === 'warning' ? 'bg-yellow-100' : 'bg-red-100') }} rounded-full flex items-center justify-center">
                    <i class="fas {{ $systemHealth['overall_status'] === 'healthy' ? 'fa-check-circle text-green-600' : ($systemHealth['overall_status'] === 'warning' ? 'fa-exclamation-triangle text-yellow-600' : 'fa-times-circle text-red-600') }} text-xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">CPU Usage</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $performanceMetrics['cpu_usage'] }}%</p>
                </div>
                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-microchip text-blue-600 text-xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Memory Usage</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $performanceMetrics['memory_usage']['percentage'] }}%</p>
                    <p class="text-xs text-gray-500">{{ $performanceMetrics['memory_usage']['used_formatted'] }} / {{ $performanceMetrics['memory_usage']['limit_formatted'] }}</p>
                </div>
                <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-memory text-purple-600 text-xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Disk Usage</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $performanceMetrics['disk_usage']['percentage'] }}%</p>
                    <p class="text-xs text-gray-500">{{ $performanceMetrics['disk_usage']['used_formatted'] }} / {{ $performanceMetrics['disk_usage']['total_formatted'] }}</p>
                </div>
                <div class="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-hdd text-orange-600 text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Real-time Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div class="text-3xl font-bold text-blue-600">{{ $realtimeStats['active_users'] }}</div>
            <div class="text-sm text-gray-600">Active Users</div>
        </div>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div class="text-3xl font-bold text-green-600">{{ $realtimeStats['total_companies'] }}</div>
            <div class="text-sm text-gray-600">Total Companies</div>
        </div>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div class="text-3xl font-bold text-purple-600">{{ $realtimeStats['messages_today'] }}</div>
            <div class="text-sm text-gray-600">Messages Today</div>
        </div>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div class="text-3xl font-bold text-yellow-600">{{ $realtimeStats['active_channels'] }}</div>
            <div class="text-sm text-gray-600">Active Channels</div>
        </div>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div class="text-3xl font-bold text-red-600">{{ $performanceMetrics['active_connections'] }}</div>
            <div class="text-sm text-gray-600">Active Connections</div>
        </div>
    </div>

    <!-- System Health Details -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">System Health</h3>
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">Database</span>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $systemHealth['database'] === 'healthy' ? 'bg-green-100 text-green-800' : ($systemHealth['database'] === 'warning' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                        {{ ucfirst($systemHealth['database']) }}
                    </span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">Cache</span>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $systemHealth['cache'] === 'healthy' ? 'bg-green-100 text-green-800' : ($systemHealth['cache'] === 'warning' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                        {{ ucfirst($systemHealth['cache']) }}
                    </span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">Storage</span>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $systemHealth['storage'] === 'healthy' ? 'bg-green-100 text-green-800' : ($systemHealth['storage'] === 'warning' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                        {{ ucfirst($systemHealth['storage']) }}
                    </span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">Queue</span>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $systemHealth['queue'] === 'healthy' ? 'bg-green-100 text-green-800' : ($systemHealth['queue'] === 'warning' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                        {{ ucfirst($systemHealth['queue']) }}
                    </span>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Server Information</h3>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">PHP Version</span>
                    <span class="text-sm text-gray-900">{{ $serverInfo['php_version'] }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Laravel Version</span>
                    <span class="text-sm text-gray-900">{{ $serverInfo['laravel_version'] }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Server Software</span>
                    <span class="text-sm text-gray-900">{{ $serverInfo['server_software'] }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Operating System</span>
                    <span class="text-sm text-gray-900">{{ $serverInfo['operating_system'] }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Timezone</span>
                    <span class="text-sm text-gray-900">{{ $serverInfo['timezone'] }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Environment</span>
                    <span class="text-sm text-gray-900">{{ ucfirst($serverInfo['environment']) }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Metrics -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Performance Metrics</h3>
            <div class="flex space-x-2">
                <a href="{{ route('admin.monitoring.system-health') }}" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-heartbeat mr-2"></i>
                    Detailed Health
                </a>
                <a href="{{ route('admin.monitoring.performance') }}" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-tachometer-alt mr-2"></i>
                    Performance Details
                </a>
            </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="text-center p-4 bg-gray-50 rounded-lg">
                <div class="text-2xl font-bold text-gray-900">{{ $performanceMetrics['response_time'] }}ms</div>
                <div class="text-sm text-gray-600">Avg Response Time</div>
            </div>
            <div class="text-center p-4 bg-gray-50 rounded-lg">
                <div class="text-2xl font-bold text-gray-900">{{ $realtimeStats['system_uptime'] }}</div>
                <div class="text-sm text-gray-600">System Uptime</div>
            </div>
            <div class="text-center p-4 bg-gray-50 rounded-lg">
                <div class="text-2xl font-bold text-gray-900">99.9%</div>
                <div class="text-sm text-gray-600">Availability</div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <button onclick="refreshData()" class="flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-sync-alt mr-2"></i>
                Refresh Data
            </button>
            <button onclick="clearCache()" class="flex items-center justify-center px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors">
                <i class="fas fa-broom mr-2"></i>
                Clear Cache
            </button>
            <button onclick="downloadLogs()" class="flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                <i class="fas fa-download mr-2"></i>
                Download Logs
            </button>
            <button onclick="exportReport()" class="flex items-center justify-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                <i class="fas fa-file-export mr-2"></i>
                Export Report
            </button>
        </div>
    </div>
</div>

@push('scripts')
<script>
function refreshData() {
    location.reload();
}

function clearCache() {
    if (confirm('Are you sure you want to clear the application cache?')) {
        fetch('{{ route("admin.settings.index") }}/clear-cache', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Cache cleared successfully!');
                location.reload();
            } else {
                alert('Failed to clear cache: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error: ' + error.message);
        });
    }
}

function downloadLogs() {
    alert('Log download functionality would be implemented here.');
}

function exportReport() {
    alert('Report export functionality would be implemented here.');
}

// Auto-refresh every 30 seconds
setInterval(function() {
    location.reload();
}, 30000);
</script>
@endpush
@endsection
