@extends('layouts.admin')

@section('page-title', 'Edit ' . $client->name)

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center space-x-2 text-sm text-gray-600 mb-2">
            <a href="{{ route('admin.clients.index') }}" class="hover:text-blue-600">Client Management</a>
            <i class="fas fa-chevron-right text-xs"></i>
            <a href="{{ route('admin.clients.show', $client) }}" class="hover:text-blue-600">{{ $client->name }}</a>
            <i class="fas fa-chevron-right text-xs"></i>
            <span>Edit</span>
        </div>
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">Edit Client</h2>
                <p class="text-gray-600">Update client company information and settings</p>
            </div>
            <div class="flex items-center space-x-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ $client->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                    <i class="fas fa-circle text-xs mr-2"></i>
                    {{ $client->is_active ? 'Active' : 'Inactive' }}
                </span>
            </div>
        </div>
    </div>

    <div class="max-w-4xl">
        <form action="{{ route('admin.clients.update', $client) }}" method="POST" x-data="editClientForm()">
            @csrf
            @method('PUT')
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Company Information -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Company Information</h3>
                    
                    <!-- Company Name -->
                    <div class="mb-6">
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                            Company Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="name" name="name" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors @error('name') border-red-500 focus:ring-red-500 focus:border-red-500 @enderror"
                               placeholder="Enter company name" value="{{ old('name', $client->name) }}">
                        @error('name')
                            <p class="mt-2 text-sm text-red-600 flex items-center">
                                <i class="fas fa-exclamation-circle mr-1"></i>
                                {{ $message }}
                            </p>
                        @enderror
                    </div>

                    <!-- Company Email -->
                    <div class="mb-6">
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                            Company Email <span class="text-red-500">*</span>
                        </label>
                        <input type="email" id="email" name="email" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors @error('email') border-red-500 focus:ring-red-500 focus:border-red-500 @enderror"
                               placeholder="<EMAIL>" value="{{ old('email', $client->email) }}">
                        @error('email')
                            <p class="mt-2 text-sm text-red-600 flex items-center">
                                <i class="fas fa-exclamation-circle mr-1"></i>
                                {{ $message }}
                            </p>
                        @enderror
                    </div>

                    <!-- Contact Name -->
                    <div class="mb-6">
                        <label for="contact_name" class="block text-sm font-medium text-gray-700 mb-2">
                            Contact Name
                        </label>
                        <input type="text" id="contact_name" name="contact_name"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors @error('contact_name') border-red-500 focus:ring-red-500 focus:border-red-500 @enderror"
                               placeholder="Enter contact name" value="{{ old('contact_name', $client->contact_name) }}">
                        @error('contact_name')
                            <p class="mt-2 text-sm text-red-600 flex items-center">
                                <i class="fas fa-exclamation-circle mr-1"></i>
                                {{ $message }}
                            </p>
                        @enderror
                    </div>

                    <!-- Phone -->
                    <div class="mb-6">
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                            Phone Number
                        </label>
                        <input type="tel" id="phone" name="phone"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors @error('phone') border-red-500 focus:ring-red-500 focus:border-red-500 @enderror"
                               placeholder="+****************" value="{{ old('phone', $client->phone) }}">
                        @error('phone')
                            <p class="mt-2 text-sm text-red-600 flex items-center">
                                <i class="fas fa-exclamation-circle mr-1"></i>
                                {{ $message }}
                            </p>
                        @enderror
                    </div>

                    <!-- Address -->
                    <div class="mb-6">
                        <label for="address" class="block text-sm font-medium text-gray-700 mb-2">
                            Address
                        </label>
                        <textarea id="address" name="address" rows="3"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors @error('address') border-red-500 focus:ring-red-500 focus:border-red-500 @enderror"
                                  placeholder="Enter company address">{{ old('address', $client->address) }}</textarea>
                        @error('address')
                            <p class="mt-2 text-sm text-red-600 flex items-center">
                                <i class="fas fa-exclamation-circle mr-1"></i>
                                {{ $message }}
                            </p>
                        @enderror
                    </div>
                </div>

                <!-- Owner Information -->
                @if($client->owner)
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Owner Information</h3>
                    
                    <!-- Owner Name -->
                    <div class="mb-6">
                        <label for="owner_name" class="block text-sm font-medium text-gray-700 mb-2">
                            Owner Full Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="owner_name" name="owner_name" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors @error('owner_name') border-red-500 focus:ring-red-500 focus:border-red-500 @enderror"
                               placeholder="Enter owner's full name" value="{{ old('owner_name', $client->owner->name) }}">
                        @error('owner_name')
                            <p class="mt-2 text-sm text-red-600 flex items-center">
                                <i class="fas fa-exclamation-circle mr-1"></i>
                                {{ $message }}
                            </p>
                        @enderror
                    </div>

                    <!-- Owner Email -->
                    <div class="mb-6">
                        <label for="owner_email" class="block text-sm font-medium text-gray-700 mb-2">
                            Owner Email <span class="text-red-500">*</span>
                        </label>
                        <input type="email" id="owner_email" name="owner_email" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors @error('owner_email') border-red-500 focus:ring-red-500 focus:border-red-500 @enderror"
                               placeholder="<EMAIL>" value="{{ old('owner_email', $client->owner->email) }}">
                        @error('owner_email')
                            <p class="mt-2 text-sm text-red-600 flex items-center">
                                <i class="fas fa-exclamation-circle mr-1"></i>
                                {{ $message }}
                            </p>
                        @enderror
                    </div>

                    <!-- Owner Status -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Owner Account Status</label>
                        <div class="flex items-center space-x-4">
                            <label class="flex items-center">
                                <input type="radio" name="owner_is_active" value="1" 
                                       {{ old('owner_is_active', $client->owner->is_active) ? 'checked' : '' }}
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                <span class="ml-2 text-sm text-gray-700">Active</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="owner_is_active" value="0"
                                       {{ !old('owner_is_active', $client->owner->is_active) ? 'checked' : '' }}
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                <span class="ml-2 text-sm text-gray-700">Inactive</span>
                            </label>
                        </div>
                    </div>

                    <!-- Password Reset Option -->
                    <div class="mb-6">
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <div class="flex items-start">
                                <i class="fas fa-key text-yellow-600 text-lg mr-3 mt-0.5"></i>
                                <div>
                                    <h4 class="text-sm font-medium text-yellow-900 mb-1">Password Management</h4>
                                    <p class="text-sm text-yellow-800 mb-3">
                                        To change the owner's password, use the "Reset Password" action from the client details page.
                                    </p>
                                    <a href="{{ route('admin.clients.show', $client) }}"
                                       class="text-sm text-yellow-800 underline hover:text-yellow-900">
                                        Go to Client Details →
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @else
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Owner Information</h3>
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div class="flex items-start">
                            <i class="fas fa-exclamation-triangle text-red-600 text-lg mr-3 mt-0.5"></i>
                            <div>
                                <h4 class="text-sm font-medium text-red-900 mb-1">No Owner Assigned</h4>
                                <p class="text-sm text-red-800">
                                    This client company does not have an assigned owner. Please contact system administrator.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                @endif
            </div>

            <!-- Account Settings -->
            <div class="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Account Settings</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Account Status -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Company Account Status</label>
                        <div class="flex items-center space-x-4">
                            <label class="flex items-center">
                                <input type="radio" name="is_active" value="1" 
                                       {{ old('is_active', $client->is_active) ? 'checked' : '' }}
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                <span class="ml-2 text-sm text-gray-700">Active</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="is_active" value="0"
                                       {{ !old('is_active', $client->is_active) ? 'checked' : '' }}
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                <span class="ml-2 text-sm text-gray-700">Inactive</span>
                            </label>
                        </div>
                        <p class="mt-1 text-xs text-gray-500">
                            Inactive accounts cannot access the system
                        </p>
                    </div>

                    <!-- Notification Settings -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Notifications</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" name="email_notifications" value="1" checked
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Email notifications</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="system_alerts" value="1" checked
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">System alerts</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Account Information -->
            <div class="mt-8 bg-gray-50 rounded-lg border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Account Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
                    <div>
                        <label class="block font-medium text-gray-500 mb-1">Client ID</label>
                        <p class="text-gray-900 font-mono">#{{ $client->id }}</p>
                    </div>
                    <div>
                        <label class="block font-medium text-gray-500 mb-1">Created</label>
                        <p class="text-gray-900">{{ $client->created_at->format('M j, Y g:i A') }}</p>
                    </div>
                    <div>
                        <label class="block font-medium text-gray-500 mb-1">Last Updated</label>
                        <p class="text-gray-900">{{ $client->updated_at->format('M j, Y g:i A') }}</p>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="mt-8 flex items-center justify-between pt-6 border-t border-gray-200">
                <div class="flex items-center space-x-4">
                    <a href="{{ route('admin.clients.show', $client) }}"
                       class="px-6 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
                        Cancel
                    </a>
                    <a href="{{ route('admin.clients.index') }}"
                       class="text-sm text-gray-500 hover:text-gray-700">
                        Back to Client List
                    </a>
                </div>
                <div class="flex items-center space-x-3">
                    <button type="button" onclick="resetForm()"
                            class="px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                        Reset Changes
                    </button>
                    <button type="submit"
                            class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        <i class="fas fa-save mr-2"></i>
                        Update Client
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
function editClientForm() {
    return {
        // Add any form-specific JavaScript here
    }
}

function resetForm() {
    if (confirm('Are you sure you want to reset all changes? This will restore the original values.')) {
        document.querySelector('form').reset();
        // Restore original values
        location.reload();
    }
}
</script>
@endpush
@endsection
