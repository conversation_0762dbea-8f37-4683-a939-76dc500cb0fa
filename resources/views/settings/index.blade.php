@extends('layouts.app')

@section('title', 'Account Settings')

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center space-x-2 text-sm text-gray-600 mb-2">
            <a href="{{ route('profile.show') }}" class="hover:text-blue-600">Profile</a>
            <i class="fas fa-chevron-right text-xs"></i>
            <span>Settings</span>
        </div>
        <h2 class="text-2xl font-bold text-gray-900">Account Settings</h2>
        <p class="text-gray-600">Manage your preferences and account configuration</p>
    </div>

    <div class="max-w-4xl">
        <form action="{{ route('user.settings.update') }}" method="POST" x-data="settingsForm()">
            @csrf
            @method('PUT')
            
            <!-- General Settings -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">General Settings</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Timezone -->
                    <div>
                        <label for="timezone" class="block text-sm font-medium text-gray-700 mb-2">
                            Timezone <span class="text-red-500">*</span>
                        </label>
                        <select id="timezone" name="timezone" required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 @error('timezone') border-red-500 @enderror">
                            <option value="UTC" {{ old('timezone', $settings['timezone'] ?? 'UTC') === 'UTC' ? 'selected' : '' }}>UTC</option>
                            <option value="America/New_York" {{ old('timezone', $settings['timezone'] ?? '') === 'America/New_York' ? 'selected' : '' }}>Eastern Time (EST/EDT)</option>
                            <option value="America/Chicago" {{ old('timezone', $settings['timezone'] ?? '') === 'America/Chicago' ? 'selected' : '' }}>Central Time (CST/CDT)</option>
                            <option value="America/Denver" {{ old('timezone', $settings['timezone'] ?? '') === 'America/Denver' ? 'selected' : '' }}>Mountain Time (MST/MDT)</option>
                            <option value="America/Los_Angeles" {{ old('timezone', $settings['timezone'] ?? '') === 'America/Los_Angeles' ? 'selected' : '' }}>Pacific Time (PST/PDT)</option>
                            <option value="Europe/London" {{ old('timezone', $settings['timezone'] ?? '') === 'Europe/London' ? 'selected' : '' }}>London (GMT/BST)</option>
                            <option value="Europe/Paris" {{ old('timezone', $settings['timezone'] ?? '') === 'Europe/Paris' ? 'selected' : '' }}>Paris (CET/CEST)</option>
                            <option value="Asia/Tokyo" {{ old('timezone', $settings['timezone'] ?? '') === 'Asia/Tokyo' ? 'selected' : '' }}>Tokyo (JST)</option>
                            <option value="Australia/Sydney" {{ old('timezone', $settings['timezone'] ?? '') === 'Australia/Sydney' ? 'selected' : '' }}>Sydney (AEST/AEDT)</option>
                        </select>
                        @error('timezone')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Language -->
                    <div>
                        <label for="language" class="block text-sm font-medium text-gray-700 mb-2">
                            Language <span class="text-red-500">*</span>
                        </label>
                        <select id="language" name="language" required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 @error('language') border-red-500 @enderror">
                            <option value="en" {{ old('language', $settings['language'] ?? 'en') === 'en' ? 'selected' : '' }}>English</option>
                            <option value="es" {{ old('language', $settings['language'] ?? '') === 'es' ? 'selected' : '' }}>Spanish</option>
                            <option value="fr" {{ old('language', $settings['language'] ?? '') === 'fr' ? 'selected' : '' }}>French</option>
                            <option value="de" {{ old('language', $settings['language'] ?? '') === 'de' ? 'selected' : '' }}>German</option>
                            <option value="it" {{ old('language', $settings['language'] ?? '') === 'it' ? 'selected' : '' }}>Italian</option>
                            <option value="pt" {{ old('language', $settings['language'] ?? '') === 'pt' ? 'selected' : '' }}>Portuguese</option>
                        </select>
                        @error('language')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Theme -->
                    <div>
                        <label for="theme" class="block text-sm font-medium text-gray-700 mb-2">
                            Theme <span class="text-red-500">*</span>
                        </label>
                        <select id="theme" name="theme" required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 @error('theme') border-red-500 @enderror">
                            <option value="light" {{ old('theme', $settings['theme'] ?? 'light') === 'light' ? 'selected' : '' }}>Light</option>
                            <option value="dark" {{ old('theme', $settings['theme'] ?? '') === 'dark' ? 'selected' : '' }}>Dark</option>
                            <option value="auto" {{ old('theme', $settings['theme'] ?? '') === 'auto' ? 'selected' : '' }}>Auto (System)</option>
                        </select>
                        @error('theme')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Date Format -->
                    <div>
                        <label for="date_format" class="block text-sm font-medium text-gray-700 mb-2">
                            Date Format
                        </label>
                        <select id="date_format" name="date_format"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="M j, Y" {{ old('date_format', $settings['date_format'] ?? 'M j, Y') === 'M j, Y' ? 'selected' : '' }}>Jan 1, 2024</option>
                            <option value="d/m/Y" {{ old('date_format', $settings['date_format'] ?? '') === 'd/m/Y' ? 'selected' : '' }}>01/01/2024</option>
                            <option value="m/d/Y" {{ old('date_format', $settings['date_format'] ?? '') === 'm/d/Y' ? 'selected' : '' }}>01/01/2024 (US)</option>
                            <option value="Y-m-d" {{ old('date_format', $settings['date_format'] ?? '') === 'Y-m-d' ? 'selected' : '' }}>2024-01-01</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Notification Settings -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Notification Preferences</h3>
                
                <div class="space-y-6">
                    <!-- Email Notifications -->
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">Email Notifications</h4>
                            <p class="text-sm text-gray-500">Receive notifications and updates via email</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" name="email_notifications" value="1" 
                                   {{ old('email_notifications', $settings['email_notifications'] ?? true) ? 'checked' : '' }}
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>

                    <!-- SMS Notifications -->
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">SMS Notifications</h4>
                            <p class="text-sm text-gray-500">Receive important alerts via SMS</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" name="sms_notifications" value="1"
                                   {{ old('sms_notifications', $settings['sms_notifications'] ?? false) ? 'checked' : '' }}
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>

                    <!-- Push Notifications -->
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">Push Notifications</h4>
                            <p class="text-sm text-gray-500">Receive push notifications in your browser</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" name="push_notifications" value="1"
                                   {{ old('push_notifications', $settings['push_notifications'] ?? false) ? 'checked' : '' }}
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                </div>

                <!-- Notification Types -->
                <div class="mt-6">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Notification Types</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <label class="flex items-center">
                            <input type="checkbox" name="notify_new_messages" value="1" 
                                   {{ old('notify_new_messages', $settings['notify_new_messages'] ?? true) ? 'checked' : '' }}
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">New messages</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="notify_system_updates" value="1"
                                   {{ old('notify_system_updates', $settings['notify_system_updates'] ?? true) ? 'checked' : '' }}
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">System updates</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="notify_security_alerts" value="1"
                                   {{ old('notify_security_alerts', $settings['notify_security_alerts'] ?? true) ? 'checked' : '' }}
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">Security alerts</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="notify_marketing" value="1"
                                   {{ old('notify_marketing', $settings['notify_marketing'] ?? false) ? 'checked' : '' }}
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">Marketing updates</span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Privacy Settings -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Privacy Settings</h3>
                
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">Profile Visibility</h4>
                            <p class="text-sm text-gray-500">Control who can see your profile information</p>
                        </div>
                        <select name="profile_visibility" class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="public" {{ old('profile_visibility', $settings['profile_visibility'] ?? 'team') === 'public' ? 'selected' : '' }}>Public</option>
                            <option value="team" {{ old('profile_visibility', $settings['profile_visibility'] ?? 'team') === 'team' ? 'selected' : '' }}>Team Only</option>
                            <option value="private" {{ old('profile_visibility', $settings['profile_visibility'] ?? '') === 'private' ? 'selected' : '' }}>Private</option>
                        </select>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">Activity Status</h4>
                            <p class="text-sm text-gray-500">Show when you're online to other users</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" name="show_activity_status" value="1"
                                   {{ old('show_activity_status', $settings['show_activity_status'] ?? true) ? 'checked' : '' }}
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">Data Analytics</h4>
                            <p class="text-sm text-gray-500">Allow us to collect usage data to improve the service</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" name="allow_analytics" value="1"
                                   {{ old('allow_analytics', $settings['allow_analytics'] ?? true) ? 'checked' : '' }}
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Account Information -->
            <div class="bg-gray-50 rounded-lg border border-gray-200 p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Account Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
                    <div>
                        <label class="block font-medium text-gray-500 mb-1">User ID</label>
                        <p class="text-gray-900 font-mono">#{{ $user->id }}</p>
                    </div>
                    <div>
                        <label class="block font-medium text-gray-500 mb-1">Account Created</label>
                        <p class="text-gray-900">{{ $user->created_at->format('M j, Y g:i A') }}</p>
                    </div>
                    <div>
                        <label class="block font-medium text-gray-500 mb-1">Last Updated</label>
                        <p class="text-gray-900">{{ $user->updated_at->format('M j, Y g:i A') }}</p>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                <div class="flex items-center space-x-4">
                    <a href="{{ route('profile.show') }}"
                       class="px-6 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
                        Back to Profile
                    </a>
                </div>
                <div class="flex items-center space-x-3">
                    <button type="button" onclick="resetSettings()"
                            class="px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                        Reset to Defaults
                    </button>
                    <button type="submit"
                            class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        <i class="fas fa-save mr-2"></i>
                        Save Settings
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
function settingsForm() {
    return {
        // Add any form-specific JavaScript here
    }
}

function resetSettings() {
    if (confirm('Are you sure you want to reset all settings to default values?')) {
        // Reset form to default values
        document.querySelector('form').reset();
    }
}
</script>
@endpush
@endsection
