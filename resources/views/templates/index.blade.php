@extends('layouts.app')

@section('title', 'Message Templates')

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">Message Templates</h2>
                <p class="text-gray-600">Create and manage reusable message templates for all channels</p>
            </div>
            <a href="{{ route('templates.create') }}" 
               class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-plus mr-2"></i>
                Create Template
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <form method="GET" class="flex flex-wrap gap-4">
            <div class="flex-1 min-w-64">
                <input type="text" name="search" placeholder="Search templates..." 
                       value="{{ request('search') }}"
                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <div>
                <select name="type" class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Types</option>
                    @foreach($templateTypes as $key => $label)
                        <option value="{{ $key }}" {{ request('type') === $key ? 'selected' : '' }}>
                            {{ $label }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div>
                <select name="status" class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Status</option>
                    <option value="draft" {{ request('status') === 'draft' ? 'selected' : '' }}>Draft</option>
                    <option value="approved" {{ request('status') === 'approved' ? 'selected' : '' }}>Approved</option>
                    <option value="rejected" {{ request('status') === 'rejected' ? 'selected' : '' }}>Rejected</option>
                </select>
            </div>
            <button type="submit" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                <i class="fas fa-search mr-2"></i>
                Filter
            </button>
            @if(request()->hasAny(['search', 'type', 'status']))
                <a href="{{ route('templates.index') }}" 
                   class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                    Clear
                </a>
            @endif
        </form>
    </div>

    <!-- Templates Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        @forelse($templates as $template)
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <!-- Template Header -->
            <div class="flex items-start justify-between mb-4">
                <div class="flex-1">
                    <h3 class="text-lg font-semibold text-gray-900 mb-1">{{ $template->name }}</h3>
                    <div class="flex items-center space-x-2">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $template->type === 'whatsapp' ? 'bg-green-100 text-green-800' : ($template->type === 'sms' ? 'bg-blue-100 text-blue-800' : ($template->type === 'email' ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800')) }}">
                            <i class="fas {{ $template->type === 'whatsapp' ? 'fa-whatsapp' : ($template->type === 'sms' ? 'fa-sms' : ($template->type === 'email' ? 'fa-envelope' : 'fa-comments')) }} mr-1"></i>
                            {{ ucfirst($template->type) }}
                        </span>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $template->status === 'approved' ? 'bg-green-100 text-green-800' : ($template->status === 'rejected' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800') }}">
                            {{ ucfirst($template->status) }}
                        </span>
                    </div>
                </div>
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <div x-show="open" @click.away="open = false" x-transition
                         class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                        <div class="py-1">
                            <a href="{{ route('templates.show', $template) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                <i class="fas fa-eye mr-2"></i>View
                            </a>
                            <a href="{{ route('templates.edit', $template) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                <i class="fas fa-edit mr-2"></i>Edit
                            </a>
                            <form action="{{ route('templates.duplicate', $template) }}" method="POST" class="inline">
                                @csrf
                                <button type="submit" class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                    <i class="fas fa-copy mr-2"></i>Duplicate
                                </button>
                            </form>
                            @if($template->status === 'draft')
                            <form action="{{ route('templates.approve', $template) }}" method="POST" class="inline">
                                @csrf
                                <button type="submit" class="w-full text-left px-4 py-2 text-sm text-green-700 hover:bg-gray-50">
                                    <i class="fas fa-check mr-2"></i>Approve
                                </button>
                            </form>
                            @endif
                            <form action="{{ route('templates.destroy', $template) }}" method="POST" class="inline" 
                                  onsubmit="return confirm('Are you sure you want to delete this template?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-gray-50">
                                    <i class="fas fa-trash mr-2"></i>Delete
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Template Content Preview -->
            <div class="mb-4">
                <p class="text-sm text-gray-600 line-clamp-3">
                    {{ Str::limit($template->content, 120) }}
                </p>
            </div>

            <!-- Template Variables -->
            @if($template->variables && count($template->variables) > 0)
            <div class="mb-4">
                <p class="text-xs text-gray-500 mb-2">Variables:</p>
                <div class="flex flex-wrap gap-1">
                    @foreach($template->variables as $variable)
                    <span class="inline-flex items-center px-2 py-1 rounded text-xs bg-blue-50 text-blue-700">
                        {{{{ $variable }}}}
                    </span>
                    @endforeach
                </div>
            </div>
            @endif

            <!-- Template Footer -->
            <div class="flex items-center justify-between text-xs text-gray-500">
                <span>Created {{ $template->created_at->diffForHumans() }}</span>
                <span>{{ $template->is_active ? 'Active' : 'Inactive' }}</span>
            </div>
        </div>
        @empty
        <div class="col-span-full">
            <div class="text-center py-12">
                <i class="fas fa-file-alt text-gray-300 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
                <p class="text-gray-500 mb-4">Get started by creating your first message template.</p>
                <a href="{{ route('templates.create') }}" 
                   class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-plus mr-2"></i>
                    Create Template
                </a>
            </div>
        </div>
        @endforelse
    </div>

    <!-- Pagination -->
    @if($templates->hasPages())
    <div class="mt-8">
        {{ $templates->appends(request()->query())->links() }}
    </div>
    @endif
</div>

@push('scripts')
<script>
// Add any template-specific JavaScript here
</script>
@endpush
@endsection
