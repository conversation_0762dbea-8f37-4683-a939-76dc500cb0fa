@extends('layouts.app')

@section('title', 'Messages')

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">Messages</h2>
                <p class="text-gray-600">View and manage all messages across channels</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('messages.create') }}" 
                   class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-plus mr-2"></i>
                    Send Message
                </a>
                <button onclick="showStatsModal()" 
                        class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-chart-bar mr-2"></i>
                    Statistics
                </button>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
            <div class="lg:col-span-2">
                <input type="text" name="search" placeholder="Search messages..." 
                       value="{{ request('search') }}"
                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <div>
                <select name="channel_id" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Channels</option>
                    @foreach($channels as $channel)
                        <option value="{{ $channel->id }}" {{ request('channel_id') == $channel->id ? 'selected' : '' }}>
                            {{ $channel->name }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div>
                <select name="direction" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Directions</option>
                    <option value="inbound" {{ request('direction') === 'inbound' ? 'selected' : '' }}>Inbound</option>
                    <option value="outbound" {{ request('direction') === 'outbound' ? 'selected' : '' }}>Outbound</option>
                </select>
            </div>
            <div>
                <select name="status" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Status</option>
                    <option value="queued" {{ request('status') === 'queued' ? 'selected' : '' }}>Queued</option>
                    <option value="sent" {{ request('status') === 'sent' ? 'selected' : '' }}>Sent</option>
                    <option value="delivered" {{ request('status') === 'delivered' ? 'selected' : '' }}>Delivered</option>
                    <option value="failed" {{ request('status') === 'failed' ? 'selected' : '' }}>Failed</option>
                    <option value="scheduled" {{ request('status') === 'scheduled' ? 'selected' : '' }}>Scheduled</option>
                </select>
            </div>
            <div class="flex space-x-2">
                <button type="submit" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="fas fa-search"></i>
                </button>
                @if(request()->hasAny(['search', 'channel_id', 'direction', 'status']))
                    <a href="{{ route('messages.index') }}" 
                       class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                        <i class="fas fa-times"></i>
                    </a>
                @endif
            </div>
        </form>
    </div>

    <!-- Messages Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Message
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Channel
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Direction
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Date
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($messages as $message)
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4">
                            <div class="flex items-start">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                                    <i class="fas {{ $message->type === 'text' ? 'fa-comment' : ($message->type === 'image' ? 'fa-image' : ($message->type === 'document' ? 'fa-file' : 'fa-paperclip')) }} text-blue-600 text-sm"></i>
                                </div>
                                <div class="min-w-0 flex-1">
                                    <p class="text-sm font-medium text-gray-900">
                                        {{ $message->direction === 'inbound' ? 'From' : 'To' }}: {{ $message->recipient }}
                                    </p>
                                    <p class="text-sm text-gray-600 truncate" style="max-width: 300px;">
                                        {{ Str::limit($message->content, 100) }}
                                    </p>
                                    @if($message->type !== 'text')
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mt-1">
                                        {{ ucfirst($message->type) }}
                                    </span>
                                    @endif
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                                    <i class="fas {{ $message->channel->type === 'whatsapp' ? 'fa-whatsapp' : ($message->channel->type === 'sms' ? 'fa-sms' : ($message->channel->type === 'email' ? 'fa-envelope' : 'fa-comments')) }} text-blue-600 text-xs"></i>
                                </div>
                                <span class="text-sm text-gray-900">{{ $message->channel->name }}</span>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $message->direction === 'inbound' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800' }}">
                                <i class="fas {{ $message->direction === 'inbound' ? 'fa-arrow-down' : 'fa-arrow-up' }} mr-1"></i>
                                {{ ucfirst($message->direction) }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                                {{ $message->status === 'delivered' ? 'bg-green-100 text-green-800' : 
                                   ($message->status === 'sent' ? 'bg-blue-100 text-blue-800' : 
                                   ($message->status === 'failed' ? 'bg-red-100 text-red-800' : 
                                   ($message->status === 'scheduled' ? 'bg-purple-100 text-purple-800' : 'bg-yellow-100 text-yellow-800'))) }}">
                                <i class="fas {{ $message->status === 'delivered' ? 'fa-check-double' : ($message->status === 'sent' ? 'fa-check' : ($message->status === 'failed' ? 'fa-times' : ($message->status === 'scheduled' ? 'fa-clock' : 'fa-hourglass-half'))) }} mr-1"></i>
                                {{ ucfirst($message->status) }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <div>
                                {{ $message->created_at->format('M j, Y') }}
                            </div>
                            <div class="text-xs">
                                {{ $message->created_at->format('g:i A') }}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2">
                                <a href="{{ route('messages.show', $message) }}" 
                                   class="text-blue-600 hover:text-blue-900">
                                    <i class="fas fa-eye"></i>
                                </a>
                                @if($message->chat)
                                <a href="{{ route('chats.show', $message->chat) }}" 
                                   class="text-green-600 hover:text-green-900" title="View Chat">
                                    <i class="fas fa-comments"></i>
                                </a>
                                @endif
                                @if(in_array($message->status, ['scheduled', 'failed']))
                                <form action="{{ route('messages.destroy', $message) }}" method="POST" class="inline"
                                      onsubmit="return confirm('Are you sure you want to delete this message?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="text-red-600 hover:text-red-900">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                @endif
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="6" class="px-6 py-12 text-center">
                            <i class="fas fa-comments text-gray-300 text-4xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No messages found</h3>
                            <p class="text-gray-500">No messages match your current filters.</p>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    @if($messages->hasPages())
    <div class="mt-6">
        {{ $messages->appends(request()->query())->links() }}
    </div>
    @endif
</div>

<!-- Statistics Modal -->
<div id="statsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Message Statistics</h3>
                    <button onclick="closeStatsModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div id="statsContent" class="grid grid-cols-2 md:grid-cols-3 gap-4">
                    <!-- Stats will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function showStatsModal() {
    document.getElementById('statsModal').classList.remove('hidden');
    loadStats();
}

function closeStatsModal() {
    document.getElementById('statsModal').classList.add('hidden');
}

function loadStats() {
    fetch('{{ route("messages.stats") }}', {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        const statsContent = document.getElementById('statsContent');
        statsContent.innerHTML = `
            <div class="text-center p-4 bg-blue-50 rounded-lg">
                <div class="text-2xl font-bold text-blue-600">${data.total}</div>
                <div class="text-sm text-blue-800">Total Messages</div>
            </div>
            <div class="text-center p-4 bg-green-50 rounded-lg">
                <div class="text-2xl font-bold text-green-600">${data.sent}</div>
                <div class="text-sm text-green-800">Sent</div>
            </div>
            <div class="text-center p-4 bg-purple-50 rounded-lg">
                <div class="text-2xl font-bold text-purple-600">${data.delivered}</div>
                <div class="text-sm text-purple-800">Delivered</div>
            </div>
            <div class="text-center p-4 bg-red-50 rounded-lg">
                <div class="text-2xl font-bold text-red-600">${data.failed}</div>
                <div class="text-sm text-red-800">Failed</div>
            </div>
            <div class="text-center p-4 bg-yellow-50 rounded-lg">
                <div class="text-2xl font-bold text-yellow-600">${data.queued}</div>
                <div class="text-sm text-yellow-800">Queued</div>
            </div>
            <div class="text-center p-4 bg-indigo-50 rounded-lg">
                <div class="text-2xl font-bold text-indigo-600">${data.delivery_rate}%</div>
                <div class="text-sm text-indigo-800">Delivery Rate</div>
            </div>
        `;
    })
    .catch(error => {
        console.error('Error loading stats:', error);
    });
}
</script>
@endpush
@endsection
