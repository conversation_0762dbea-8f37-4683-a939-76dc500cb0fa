@extends('layouts.app')

@section('title', 'Chatbots')

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">Chatbots</h2>
                <p class="text-gray-600">Create and manage automated chatbots for your channels</p>
            </div>
            <a href="{{ route('chatbots.create') }}" 
               class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-robot mr-2"></i>
                Create Chatbot
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <form method="GET" class="flex flex-wrap gap-4">
            <div class="flex-1 min-w-64">
                <input type="text" name="search" placeholder="Search chatbots..." 
                       value="{{ request('search') }}"
                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <div>
                <select name="channel_id" class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Channels</option>
                    @foreach($channels as $channel)
                        <option value="{{ $channel->id }}" {{ request('channel_id') == $channel->id ? 'selected' : '' }}>
                            {{ $channel->name }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div>
                <select name="status" class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Status</option>
                    <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                    <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                </select>
            </div>
            <button type="submit" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                <i class="fas fa-search mr-2"></i>
                Filter
            </button>
            @if(request()->hasAny(['search', 'channel_id', 'status']))
                <a href="{{ route('chatbots.index') }}" 
                   class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                    Clear
                </a>
            @endif
        </form>
    </div>

    <!-- Chatbots Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        @forelse($chatbots as $chatbot)
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <!-- Chatbot Header -->
            <div class="flex items-start justify-between mb-4">
                <div class="flex-1">
                    <div class="flex items-center mb-2">
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-robot text-blue-600"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">{{ $chatbot->name }}</h3>
                            <p class="text-sm text-gray-500">{{ $chatbot->channel->name }}</p>
                        </div>
                    </div>
                    @if($chatbot->description)
                    <p class="text-sm text-gray-600 mb-3">{{ Str::limit($chatbot->description, 100) }}</p>
                    @endif
                </div>
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <div x-show="open" @click.away="open = false" x-transition
                         class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                        <div class="py-1">
                            <a href="{{ route('chatbots.show', $chatbot) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                <i class="fas fa-eye mr-2"></i>View Details
                            </a>
                            <a href="{{ route('chatbots.builder', $chatbot) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                <i class="fas fa-project-diagram mr-2"></i>Flow Builder
                            </a>
                            <a href="{{ route('chatbots.edit', $chatbot) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                <i class="fas fa-edit mr-2"></i>Edit
                            </a>
                            <button onclick="testChatbot({{ $chatbot->id }})" class="w-full text-left px-4 py-2 text-sm text-blue-700 hover:bg-gray-50">
                                <i class="fas fa-play mr-2"></i>Test Bot
                            </button>
                            <form action="{{ route('chatbots.toggle', $chatbot) }}" method="POST" class="inline">
                                @csrf
                                <button type="submit" class="w-full text-left px-4 py-2 text-sm {{ $chatbot->is_active ? 'text-red-700' : 'text-green-700' }} hover:bg-gray-50">
                                    <i class="fas {{ $chatbot->is_active ? 'fa-pause' : 'fa-play' }} mr-2"></i>
                                    {{ $chatbot->is_active ? 'Deactivate' : 'Activate' }}
                                </button>
                            </form>
                            <form action="{{ route('chatbots.destroy', $chatbot) }}" method="POST" class="inline" 
                                  onsubmit="return confirm('Are you sure you want to delete this chatbot?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-gray-50">
                                    <i class="fas fa-trash mr-2"></i>Delete
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status and Stats -->
            <div class="flex items-center justify-between mb-4">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $chatbot->is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                    <i class="fas fa-circle text-xs mr-1"></i>
                    {{ $chatbot->is_active ? 'Active' : 'Inactive' }}
                </span>
                <span class="text-sm text-gray-500">
                    {{ $chatbot->conversions }} conversions
                </span>
            </div>

            <!-- Channel Info -->
            <div class="flex items-center mb-4 p-3 bg-gray-50 rounded-lg">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <i class="fas {{ $chatbot->channel->type === 'whatsapp' ? 'fa-whatsapp' : ($chatbot->channel->type === 'telegram' ? 'fa-telegram' : ($chatbot->channel->type === 'webchat' ? 'fa-comments' : 'fa-sms')) }} text-blue-600 text-sm"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-900">{{ $chatbot->channel->name }}</p>
                    <p class="text-xs text-gray-500">{{ ucfirst($chatbot->channel->type) }} Channel</p>
                </div>
            </div>

            <!-- Flow Info -->
            <div class="mb-4">
                <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-600">Flow Nodes:</span>
                    <span class="font-medium">{{ count($chatbot->flow_data['nodes'] ?? []) }}</span>
                </div>
                <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-600">Last Updated:</span>
                    <span class="font-medium">{{ $chatbot->updated_at->diffForHumans() }}</span>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="flex space-x-2">
                <a href="{{ route('chatbots.builder', $chatbot) }}" 
                   class="flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors text-center">
                    <i class="fas fa-project-diagram mr-1"></i>
                    Builder
                </a>
                <button onclick="testChatbot({{ $chatbot->id }})" 
                        class="flex-1 px-3 py-2 bg-green-600 text-white text-sm rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-play mr-1"></i>
                    Test
                </button>
            </div>
        </div>
        @empty
        <div class="col-span-full">
            <div class="text-center py-12">
                <i class="fas fa-robot text-gray-300 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No chatbots found</h3>
                <p class="text-gray-500 mb-4">Get started by creating your first chatbot.</p>
                <a href="{{ route('chatbots.create') }}" 
                   class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-robot mr-2"></i>
                    Create Chatbot
                </a>
            </div>
        </div>
        @endforelse
    </div>

    <!-- Pagination -->
    @if($chatbots->hasPages())
    <div class="mt-8">
        {{ $chatbots->appends(request()->query())->links() }}
    </div>
    @endif
</div>

<!-- Test Chatbot Modal -->
<div id="testModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Test Chatbot</h3>
                    <button onclick="closeTestModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="mb-4">
                    <label for="test_message" class="block text-sm font-medium text-gray-700 mb-2">
                        Test Message
                    </label>
                    <input type="text" id="test_message" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="Hello" value="Hello">
                </div>
                
                <div id="test_response" class="mb-4 p-3 bg-gray-50 rounded-lg hidden">
                    <p class="text-sm text-gray-600 mb-2">Bot Response:</p>
                    <div id="response_content" class="text-sm"></div>
                </div>
            </div>
            
            <div class="px-6 py-4 bg-gray-50 rounded-b-lg flex justify-end space-x-3">
                <button onclick="closeTestModal()"
                        class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors">
                    Cancel
                </button>
                <button onclick="sendTestMessage()"
                        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    Send Test
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
let currentChatbotId = null;

function testChatbot(chatbotId) {
    currentChatbotId = chatbotId;
    document.getElementById('testModal').classList.remove('hidden');
    document.getElementById('test_response').classList.add('hidden');
}

function closeTestModal() {
    document.getElementById('testModal').classList.add('hidden');
    currentChatbotId = null;
}

function sendTestMessage() {
    if (!currentChatbotId) return;
    
    const message = document.getElementById('test_message').value;
    
    fetch(`/chatbots/${currentChatbotId}/test`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ message: message })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('response_content').innerHTML = 
                `<div class="p-2 bg-blue-100 rounded">${data.response.message}</div>`;
            document.getElementById('test_response').classList.remove('hidden');
        } else {
            alert('Test failed');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred during testing');
    });
}
</script>
@endpush
@endsection
