@extends('layouts.app')

@section('title', 'Embed Code Generator')

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">Web Chat Embed Code</h2>
                <p class="text-gray-600">Generate embed codes for your web chat widgets</p>
            </div>
        </div>
    </div>

    @if($webChatChannels->count() > 0)
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Configuration Form -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Widget Configuration</h3>
            
            <form id="embedForm" x-data="embedGenerator()">
                <!-- Channel Selection -->
                <div class="mb-6">
                    <label for="channel_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Web Chat Channel <span class="text-red-500">*</span>
                    </label>
                    <select id="channel_id" name="channel_id" required x-model="config.channel_id"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Select a channel</option>
                        @foreach($webChatChannels as $channel)
                        <option value="{{ $channel->id }}">{{ $channel->name }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- Widget Appearance -->
                <div class="mb-6">
                    <h4 class="text-md font-medium text-gray-900 mb-3">Widget Appearance</h4>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Position -->
                        <div>
                            <label for="widget_position" class="block text-sm font-medium text-gray-700 mb-2">
                                Position
                            </label>
                            <select id="widget_position" name="widget_position" x-model="config.widget_position"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="bottom-right">Bottom Right</option>
                                <option value="bottom-left">Bottom Left</option>
                                <option value="top-right">Top Right</option>
                                <option value="top-left">Top Left</option>
                            </select>
                        </div>

                        <!-- Color -->
                        <div>
                            <label for="widget_color" class="block text-sm font-medium text-gray-700 mb-2">
                                Primary Color
                            </label>
                            <input type="color" id="widget_color" name="widget_color" x-model="config.widget_color"
                                   class="w-full h-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>

                    <!-- Title -->
                    <div class="mt-4">
                        <label for="widget_title" class="block text-sm font-medium text-gray-700 mb-2">
                            Widget Title
                        </label>
                        <input type="text" id="widget_title" name="widget_title" x-model="config.widget_title"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="Chat with us">
                    </div>
                </div>

                <!-- Messages -->
                <div class="mb-6">
                    <h4 class="text-md font-medium text-gray-900 mb-3">Messages</h4>
                    
                    <div class="space-y-4">
                        <!-- Welcome Message -->
                        <div>
                            <label for="welcome_message" class="block text-sm font-medium text-gray-700 mb-2">
                                Welcome Message
                            </label>
                            <textarea id="welcome_message" name="welcome_message" rows="3" x-model="config.welcome_message"
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                      placeholder="Hello! How can we help you today?"></textarea>
                        </div>

                        <!-- Offline Message -->
                        <div>
                            <label for="offline_message" class="block text-sm font-medium text-gray-700 mb-2">
                                Offline Message
                            </label>
                            <textarea id="offline_message" name="offline_message" rows="3" x-model="config.offline_message"
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                      placeholder="We're currently offline. Please leave a message."></textarea>
                        </div>
                    </div>
                </div>

                <!-- Features -->
                <div class="mb-6">
                    <h4 class="text-md font-medium text-gray-900 mb-3">Features</h4>
                    
                    <div class="space-y-3">
                        <label class="flex items-center">
                            <input type="checkbox" name="show_agent_avatar" x-model="config.show_agent_avatar"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">Show agent avatars</span>
                        </label>

                        <label class="flex items-center">
                            <input type="checkbox" name="show_typing_indicator" x-model="config.show_typing_indicator"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">Show typing indicator</span>
                        </label>

                        <label class="flex items-center">
                            <input type="checkbox" name="enable_file_upload" x-model="config.enable_file_upload"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">Enable file uploads</span>
                        </label>

                        <label class="flex items-center">
                            <input type="checkbox" name="enable_emoji" x-model="config.enable_emoji"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">Enable emoji picker</span>
                        </label>
                    </div>
                </div>

                <!-- Generate Button -->
                <button type="button" @click="generateEmbed()" :disabled="!config.channel_id"
                        class="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors">
                    <i class="fas fa-code mr-2"></i>
                    Generate Embed Code
                </button>
            </form>
        </div>

        <!-- Preview and Code -->
        <div class="space-y-6">
            <!-- Widget Preview -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Widget Preview</h3>
                <div class="relative bg-gray-100 rounded-lg p-4 min-h-64" x-data="{ showWidget: false }">
                    <div class="text-center text-gray-500 py-8">
                        <i class="fas fa-eye text-4xl mb-2"></i>
                        <p>Generate embed code to see preview</p>
                    </div>
                    
                    <!-- Preview Widget (will be populated by JavaScript) -->
                    <div id="widgetPreview" class="hidden"></div>
                </div>
            </div>

            <!-- Generated Code -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6" x-show="embedCode" x-cloak>
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Embed Code</h3>
                    <button @click="copyToClipboard()" 
                            class="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 transition-colors">
                        <i class="fas fa-copy mr-1"></i>
                        Copy
                    </button>
                </div>
                
                <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                    <pre class="text-green-400 text-sm"><code x-text="embedCode"></code></pre>
                </div>
                
                <div class="mt-4 p-3 bg-blue-50 rounded-lg">
                    <p class="text-sm text-blue-800">
                        <i class="fas fa-info-circle mr-1"></i>
                        Copy this code and paste it into your website's HTML, just before the closing &lt;/body&gt; tag.
                    </p>
                </div>
            </div>
        </div>
    </div>
    @else
    <!-- No Web Chat Channels -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
        <div class="text-center">
            <i class="fas fa-comments text-gray-300 text-6xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Web Chat Channels</h3>
            <p class="text-gray-500 mb-4">You need to create a web chat channel before generating embed codes.</p>
            <a href="{{ route('channels.create') }}" 
               class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-plus mr-2"></i>
                Create Web Chat Channel
            </a>
        </div>
    </div>
    @endif
</div>

@push('scripts')
<script>
function embedGenerator() {
    return {
        config: {
            channel_id: '',
            widget_position: 'bottom-right',
            widget_color: '#007bff',
            widget_title: 'Chat with us',
            welcome_message: 'Hello! How can we help you today?',
            offline_message: 'We\'re currently offline. Please leave a message.',
            show_agent_avatar: true,
            show_typing_indicator: true,
            enable_file_upload: false,
            enable_emoji: true
        },
        embedCode: '',
        
        generateEmbed() {
            if (!this.config.channel_id) {
                alert('Please select a web chat channel');
                return;
            }
            
            fetch('{{ route("embed.generate") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(this.config)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.embedCode = data.embed_code;
                    this.showPreview(data.preview_url);
                } else {
                    alert('Failed to generate embed code');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while generating the embed code');
            });
        },
        
        showPreview(previewUrl) {
            const preview = document.getElementById('widgetPreview');
            preview.innerHTML = `<iframe src="${previewUrl}" width="100%" height="400" frameborder="0"></iframe>`;
            preview.classList.remove('hidden');
        },
        
        copyToClipboard() {
            navigator.clipboard.writeText(this.embedCode).then(() => {
                // Show success message
                const button = event.target.closest('button');
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check mr-1"></i>Copied!';
                button.classList.remove('bg-green-600', 'hover:bg-green-700');
                button.classList.add('bg-green-700');
                
                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.classList.remove('bg-green-700');
                    button.classList.add('bg-green-600', 'hover:bg-green-700');
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy: ', err);
                alert('Failed to copy to clipboard');
            });
        }
    }
}
</script>
@endpush
@endsection
