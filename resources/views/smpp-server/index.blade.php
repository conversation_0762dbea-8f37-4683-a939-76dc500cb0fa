@extends('layouts.app')

@section('title', 'SMPP Server')

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">SMPP Server</h2>
                <p class="text-gray-600">Manage SMPP server for incoming SMS connections</p>
            </div>
            <div class="flex space-x-3">
                @if($serverStatus['status'] === 'stopped')
                <button onclick="startServer()" 
                        class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-play mr-2"></i>
                    Start Server
                </button>
                @else
                <button onclick="stopServer()" 
                        class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                    <i class="fas fa-stop mr-2"></i>
                    Stop Server
                </button>
                @endif
                <button onclick="refreshStatus()" 
                        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-sync mr-2"></i>
                    Refresh
                </button>
            </div>
        </div>
    </div>

    <!-- Server Status -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 {{ $serverStatus['status'] === 'running' ? 'bg-green-100' : 'bg-red-100' }} rounded-full flex items-center justify-center">
                    <i class="fas {{ $serverStatus['status'] === 'running' ? 'fa-play' : 'fa-stop' }} {{ $serverStatus['status'] === 'running' ? 'text-green-600' : 'text-red-600' }} text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Server Status</p>
                    <p class="text-2xl font-bold {{ $serverStatus['status'] === 'running' ? 'text-green-600' : 'text-red-600' }}">
                        {{ ucfirst($serverStatus['status']) }}
                    </p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-network-wired text-blue-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Active Connections</p>
                    <p class="text-2xl font-bold text-gray-900">{{ count($activeConnections) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-server text-purple-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Port</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $serverStatus['port'] }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-clock text-yellow-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Uptime</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $serverStatus['uptime'] ?? 'N/A' }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Server Configuration -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Server Configuration</h3>
            
            <form id="configForm" x-data="serverConfig()">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="port" class="block text-sm font-medium text-gray-700 mb-2">
                            Port <span class="text-red-500">*</span>
                        </label>
                        <input type="number" id="port" name="port" x-model="config.port" min="1024" max="65535"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div>
                        <label for="interface" class="block text-sm font-medium text-gray-700 mb-2">
                            Interface <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="interface" name="interface" x-model="config.interface"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div>
                        <label for="max_connections" class="block text-sm font-medium text-gray-700 mb-2">
                            Max Connections
                        </label>
                        <input type="number" id="max_connections" name="max_connections" x-model="config.max_connections" min="1" max="1000"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div>
                        <label for="session_timeout" class="block text-sm font-medium text-gray-700 mb-2">
                            Session Timeout (seconds)
                        </label>
                        <input type="number" id="session_timeout" name="session_timeout" x-model="config.session_timeout" min="60" max="3600"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div>
                        <label for="enquire_link_timeout" class="block text-sm font-medium text-gray-700 mb-2">
                            Enquire Link Timeout (seconds)
                        </label>
                        <input type="number" id="enquire_link_timeout" name="enquire_link_timeout" x-model="config.enquire_link_timeout" min="30" max="300"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div>
                        <label for="log_level" class="block text-sm font-medium text-gray-700 mb-2">
                            Log Level
                        </label>
                        <select id="log_level" name="log_level" x-model="config.log_level"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="DEBUG">DEBUG</option>
                            <option value="INFO">INFO</option>
                            <option value="WARN">WARN</option>
                            <option value="ERROR">ERROR</option>
                        </select>
                    </div>
                </div>

                <div class="mt-4">
                    <label class="flex items-center">
                        <input type="checkbox" name="enable_tls" x-model="config.enable_tls"
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-700">Enable TLS/SSL</span>
                    </label>
                </div>

                <div class="mt-6">
                    <button type="button" @click="saveConfig()" :disabled="serverStatus === 'running'"
                            class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors">
                        Save Configuration
                    </button>
                    <p class="text-xs text-gray-500 mt-2">Configuration can only be changed when server is stopped</p>
                </div>
            </form>
        </div>

        <!-- Statistics -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Server Statistics</h3>
            
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Messages Processed Today</span>
                    <span class="font-medium">{{ number_format($stats['messages_processed_today']) }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Total Messages Processed</span>
                    <span class="font-medium">{{ number_format($stats['messages_processed_total']) }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Error Rate</span>
                    <span class="font-medium">{{ $stats['error_rate'] * 100 }}%</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Average Response Time</span>
                    <span class="font-medium">{{ $stats['avg_response_time'] }}ms</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Peak Connections</span>
                    <span class="font-medium">{{ $stats['peak_connections'] }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Uptime Percentage</span>
                    <span class="font-medium">{{ $stats['uptime_percentage'] }}%</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Connections -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Active Connections</h3>
        
        @if(count($activeConnections) > 0)
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            System ID
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            IP Address
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Connected
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Messages
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($activeConnections as $connection)
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-user text-blue-600 text-sm"></i>
                                </div>
                                <span class="text-sm font-medium text-gray-900">{{ $connection['system_id'] }}</span>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ $connection['ip_address'] }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {{ $connection['status'] }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ \Carbon\Carbon::parse($connection['connected_at'])->diffForHumans() }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div class="flex space-x-4">
                                <span class="text-green-600">↑ {{ number_format($connection['messages_sent']) }}</span>
                                <span class="text-blue-600">↓ {{ number_format($connection['messages_received']) }}</span>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        @else
        <div class="text-center py-8">
            <i class="fas fa-network-wired text-gray-300 text-4xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Active Connections</h3>
            <p class="text-gray-500">No SMPP clients are currently connected to the server.</p>
        </div>
        @endif
    </div>
</div>

@push('scripts')
<script>
function serverConfig() {
    return {
        config: @json($config),
        serverStatus: '{{ $serverStatus["status"] }}',
        
        saveConfig() {
            // Configuration saving would be implemented here
            alert('Configuration saved successfully!');
        }
    }
}

function startServer() {
    if (!confirm('Are you sure you want to start the SMPP server?')) {
        return;
    }
    
    const config = Alpine.store('serverConfig') || @json($config);
    
    fetch('{{ route("smpp-server.start") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('SMPP server started successfully!');
            location.reload();
        } else {
            alert('Failed to start server: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while starting the server');
    });
}

function stopServer() {
    if (!confirm('Are you sure you want to stop the SMPP server? This will disconnect all clients.')) {
        return;
    }
    
    fetch('{{ route("smpp-server.stop") }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('SMPP server stopped successfully!');
            location.reload();
        } else {
            alert('Failed to stop server: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while stopping the server');
    });
}

function refreshStatus() {
    fetch('{{ route("smpp-server.status") }}', {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        // Update the page with new status data
        location.reload();
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

// Auto-refresh status every 30 seconds
setInterval(refreshStatus, 30000);
</script>
@endpush
@endsection
