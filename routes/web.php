<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\Client\DashboardController as ClientDashboardController;
use App\Http\Controllers\ChannelController;
use App\Http\Controllers\SmppClientController;
use App\Http\Controllers\ChatController;
use App\Http\Controllers\Admin\DashboardController as AdminDashboardController;
use App\Http\Controllers\Admin\ClientController as AdminClientController;

// Landing page
Route::get('/', function () {
    if (Auth::check()) {
        return redirect('/dashboard');
    }
    return view('welcome');
})->name('welcome');

// Authentication Routes
Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('/login', [LoginController::class, 'login']);
Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

Route::get('/register', [RegisterController::class, 'showRegistrationForm'])->name('register');
Route::post('/register', [RegisterController::class, 'register']);

// Dashboard Routes
Route::middleware('auth')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/client/dashboard', [ClientDashboardController::class, 'index'])->name('client.dashboard');
    Route::get('/admin/dashboard', [DashboardController::class, 'adminDashboard'])->name('admin.dashboard');
    Route::get('/supervisor/dashboard', [DashboardController::class, 'supervisorDashboard'])->name('supervisor.dashboard');
    Route::get('/agent/dashboard', [DashboardController::class, 'agentDashboard'])->name('agent.dashboard');

    // Channel Routes
    Route::resource('channels', ChannelController::class);
    Route::post('/channels/{channel}/toggle', [ChannelController::class, 'toggle'])->name('channels.toggle');
    Route::post('/channels/{channel}/test', [ChannelController::class, 'testConnection'])->name('channels.test');

    // SMPP Client Routes
    Route::resource('smpp', SmppClientController::class);
    Route::post('/smpp/{smppClient}/toggle', [SmppClientController::class, 'toggle'])->name('smpp.toggle');
    Route::post('/smpp/{smppClient}/reset-stats', [SmppClientController::class, 'resetStats'])->name('smpp.reset-stats');

    // Chat Routes
    Route::resource('chats', ChatController::class)->except(['create', 'store', 'edit', 'destroy']);
    Route::post('/chats/{chat}/assign', [ChatController::class, 'assign'])->name('chats.assign');
    Route::post('/chats/{chat}/status', [ChatController::class, 'updateStatus'])->name('chats.status');
    Route::post('/chats/{chat}/message', [ChatController::class, 'sendMessage'])->name('chats.message');
    Route::post('/chats/{chat}/notes', [ChatController::class, 'addNotes'])->name('chats.notes');
    Route::get('/api/chats/stats', [ChatController::class, 'getStats'])->name('chats.stats');
});

// Admin Routes
Route::middleware(['auth'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');

    // Client Management
    Route::resource('clients', AdminClientController::class);
    Route::post('/clients/{client}/toggle-status', [AdminClientController::class, 'toggleStatus'])->name('clients.toggle-status');
    Route::post('/clients/{client}/reset-password', [AdminClientController::class, 'resetPassword'])->name('clients.reset-password');
});
