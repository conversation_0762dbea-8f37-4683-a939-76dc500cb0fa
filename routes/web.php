<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\Client\DashboardController as ClientDashboardController;
use App\Http\Controllers\ChannelController;
use App\Http\Controllers\SmppClientController;
use App\Http\Controllers\ChatController;
use App\Http\Controllers\Admin\DashboardController as AdminDashboardController;
use App\Http\Controllers\Admin\ClientController as AdminClientController;
use App\Http\Controllers\Admin\SystemMonitoringController;
use App\Http\Controllers\Admin\UserManagementController;
use App\Http\Controllers\Admin\LandingPageController;
use App\Http\Controllers\Admin\SystemSettingsController;
use App\Http\Controllers\Admin\ReportsController;
use App\Http\Controllers\TemplateController;
use App\Http\Controllers\ChatbotController;
use App\Http\Controllers\EmbedController;
use App\Http\Controllers\MessageController;
use App\Http\Controllers\SmppServerController;

// Landing page
Route::get('/', function () {
    if (Auth::check()) {
        return redirect('/dashboard');
    }
    return view('welcome');
})->name('welcome');

// Authentication Routes
Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('/login', [LoginController::class, 'login']);
Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

Route::get('/register', [RegisterController::class, 'showRegistrationForm'])->name('register');
Route::post('/register', [RegisterController::class, 'register']);

// Public Embed Widget Routes (no auth required)
Route::get('/widget/{token}', [EmbedController::class, 'widget'])->name('embed.widget');
Route::post('/widget/{token}/api', [EmbedController::class, 'api'])->name('embed.api');

// Dashboard Routes
Route::middleware('auth')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/client/dashboard', [ClientDashboardController::class, 'index'])->name('client.dashboard');
    Route::get('/admin/dashboard', [DashboardController::class, 'adminDashboard'])->name('admin.dashboard');
    Route::get('/supervisor/dashboard', [DashboardController::class, 'supervisorDashboard'])->name('supervisor.dashboard');
    Route::get('/agent/dashboard', [DashboardController::class, 'agentDashboard'])->name('agent.dashboard');

    // Profile Routes
    Route::get('/profile', [DashboardController::class, 'profile'])->name('profile.show');
    Route::get('/profile/edit', [DashboardController::class, 'editProfile'])->name('profile.edit');
    Route::put('/profile', [DashboardController::class, 'updateProfile'])->name('profile.update');
    Route::put('/profile/password', [DashboardController::class, 'updatePassword'])->name('profile.password.update');

    // User Settings Routes
    Route::get('/settings', [DashboardController::class, 'settings'])->name('user.settings.index');
    Route::put('/settings', [DashboardController::class, 'updateSettings'])->name('user.settings.update');

    // Channel Routes
    Route::resource('channels', ChannelController::class);
    Route::post('/channels/{channel}/toggle', [ChannelController::class, 'toggle'])->name('channels.toggle');
    Route::post('/channels/{channel}/test', [ChannelController::class, 'testConnection'])->name('channels.test');

    // SMPP Client Routes
    Route::resource('smpp', SmppClientController::class);
    Route::post('/smpp/{smppClient}/toggle', [SmppClientController::class, 'toggle'])->name('smpp.toggle');
    Route::post('/smpp/{smppClient}/reset-stats', [SmppClientController::class, 'resetStats'])->name('smpp.reset-stats');

    // Chat Routes
    Route::resource('chats', ChatController::class)->except(['create', 'store', 'edit', 'destroy']);
    Route::post('/chats/{chat}/assign', [ChatController::class, 'assign'])->name('chats.assign');
    Route::post('/chats/{chat}/status', [ChatController::class, 'updateStatus'])->name('chats.status');

    // Message Routes
    Route::resource('messages', MessageController::class)->except(['create', 'edit']);
    Route::post('/messages/send', [MessageController::class, 'send'])->name('messages.send');
    Route::get('/messages/conversation/{contact}', [MessageController::class, 'conversation'])->name('messages.conversation');

    // Template Routes
    Route::resource('templates', TemplateController::class);
    Route::post('/templates/{template}/duplicate', [TemplateController::class, 'duplicate'])->name('templates.duplicate');
    Route::post('/templates/{template}/approve', [TemplateController::class, 'approve'])->name('templates.approve');

    // Chatbot Routes
    Route::resource('chatbots', ChatbotController::class);
    Route::post('/chatbots/{chatbot}/toggle', [ChatbotController::class, 'toggle'])->name('chatbots.toggle');
    Route::post('/chatbots/{chatbot}/test', [ChatbotController::class, 'test'])->name('chatbots.test');

    // Embed Code Routes
    Route::get('/embed', [EmbedController::class, 'index'])->name('embed.index');
    Route::post('/embed/generate', [EmbedController::class, 'generate'])->name('embed.generate');
    Route::get('/embed/preview/{token}', [EmbedController::class, 'preview'])->name('embed.preview');

    // Additional Template Routes
    Route::get('/templates/{template}/preview', [TemplateController::class, 'preview'])->name('templates.preview');
    Route::post('/templates/extract-variables', [TemplateController::class, 'extractVariables'])->name('templates.extract-variables');

    // Additional Chatbot Routes
    Route::get('/chatbots/{chatbot}/builder', [ChatbotController::class, 'builder'])->name('chatbots.builder');
    Route::post('/chatbots/{chatbot}/save-flow', [ChatbotController::class, 'saveFlow'])->name('chatbots.save-flow');

    // Message Statistics Route
    Route::get('/messages/stats', [MessageController::class, 'stats'])->name('messages.stats');

    // SMPP Server Routes
    Route::get('/smpp-server', [SmppServerController::class, 'index'])->name('smpp-server.index');
    Route::post('/smpp-server/start', [SmppServerController::class, 'start'])->name('smpp-server.start');
    Route::post('/smpp-server/stop', [SmppServerController::class, 'stop'])->name('smpp-server.stop');
    Route::get('/smpp-server/status', [SmppServerController::class, 'status'])->name('smpp-server.status');
    Route::post('/chats/{chat}/message', [ChatController::class, 'sendMessage'])->name('chats.message');
    Route::post('/chats/{chat}/notes', [ChatController::class, 'addNotes'])->name('chats.notes');
    Route::get('/api/chats/stats', [ChatController::class, 'getStats'])->name('chats.stats');
});

// Admin Routes
Route::middleware(['auth'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');

    // Client Management
    Route::resource('clients', AdminClientController::class);
    Route::post('/clients/{client}/toggle-status', [AdminClientController::class, 'toggleStatus'])->name('clients.toggle-status');
    Route::post('/clients/{client}/reset-password', [AdminClientController::class, 'resetPassword'])->name('clients.reset-password');

    // System Monitoring
    Route::get('/monitoring', [SystemMonitoringController::class, 'index'])->name('monitoring.index');
    Route::get('/monitoring/system-health', [SystemMonitoringController::class, 'systemHealth'])->name('monitoring.system-health');
    Route::get('/monitoring/performance', [SystemMonitoringController::class, 'performance'])->name('monitoring.performance');

    // User Management
    Route::resource('users', UserManagementController::class);
    Route::post('/users/{user}/toggle-status', [UserManagementController::class, 'toggleStatus'])->name('users.toggle-status');
    Route::post('/users/{user}/reset-password', [UserManagementController::class, 'resetPassword'])->name('users.reset-password');
    Route::get('/users/export', [UserManagementController::class, 'export'])->name('users.export');
    Route::post('/users/bulk-action', [UserManagementController::class, 'bulkAction'])->name('users.bulk-action');

    // Landing Page Editor
    Route::get('/landing-page', [LandingPageController::class, 'index'])->name('landing-page.index');
    Route::put('/landing-page', [LandingPageController::class, 'update'])->name('landing-page.update');
    Route::post('/landing-page/preview', [LandingPageController::class, 'preview'])->name('landing-page.preview');

    // System Settings
    Route::get('/settings', [SystemSettingsController::class, 'index'])->name('settings.index');
    Route::put('/settings', [SystemSettingsController::class, 'update'])->name('settings.update');
    Route::post('/settings/test-email', [SystemSettingsController::class, 'testEmail'])->name('settings.test-email');
    Route::get('/settings/export', [SystemSettingsController::class, 'export'])->name('settings.export');
    Route::post('/settings/clear-cache', [SystemSettingsController::class, 'clearCache'])->name('settings.clear-cache');
    Route::post('/settings/reset', [SystemSettingsController::class, 'reset'])->name('settings.reset');

    // Reports
    Route::get('/reports', [ReportsController::class, 'index'])->name('reports.index');
    Route::get('/reports/analytics', [ReportsController::class, 'analytics'])->name('reports.analytics');
    Route::get('/reports/export/{type}', [ReportsController::class, 'export'])->name('reports.export');
});
