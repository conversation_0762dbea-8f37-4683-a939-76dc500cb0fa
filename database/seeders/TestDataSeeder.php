<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Company;
use App\Models\User;
use App\Models\Channel;
use App\Models\Department;
use Illuminate\Support\Facades\Hash;

class TestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create or find admin user
        $adminUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => Hash::make('password'),
                'role' => 'admin',
                'is_active' => true,
            ]
        );

        // Create admin company if it doesn't exist
        $adminCompany = Company::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'MessageBay Admin',
                'contact_name' => 'Admin User',
                'phone' => '******-MSGBAY',
                'owner_id' => $adminUser->id,
                'is_active' => true,
            ]
        );

        // Update admin user with company if not set
        if (!$adminUser->company_id) {
            $adminUser->update(['company_id' => $adminCompany->id]);
        }

        // Create test company
        $company = Company::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test Company',
                'contact_name' => 'John Doe',
                'phone' => '+*********0',
                'is_active' => true,
            ]
        );

        // Create test user
        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'John Doe',
                'password' => Hash::make('password'),
                'role' => 'client',
                'company_id' => $company->id,
                'is_active' => true,
            ]
        );

        // Set the test user as the owner of the test company if not set
        if (!$company->owner_id) {
            $company->update(['owner_id' => $user->id]);
        }

        // Create test department
        $department = Department::create([
            'company_id' => $company->id,
            'name' => 'Customer Support',
            'description' => 'Main customer support department',
            'is_active' => true,
        ]);

        // Create sample channels
        Channel::create([
            'company_id' => $company->id,
            'type' => 'whatsapp',
            'name' => 'WhatsApp Business',
            'is_enabled' => true,
            'is_connected' => true,
            'config' => [
                'phone_number_id' => '*********',
                'access_token' => 'sample_token',
                'verify_token' => 'verify_123'
            ]
        ]);

        Channel::create([
            'company_id' => $company->id,
            'type' => 'webchat',
            'name' => 'Website Chat',
            'is_enabled' => true,
            'is_connected' => true,
            'config' => [
                'widget_title' => 'Chat with us!',
                'welcome_message' => 'Welcome! How can we help you today?',
                'primary_color' => '#3B82F6'
            ]
        ]);
    }
}
