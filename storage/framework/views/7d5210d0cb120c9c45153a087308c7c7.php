<?php $__env->startSection('page-title', 'SMPP Gateway'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h2 class="text-2xl font-bold text-gray-900">SMPP Gateway</h2>
            <p class="text-gray-600">Manage SMPP client connections for SMS delivery</p>
        </div>
        <a href="<?php echo e(route('smpp.create')); ?>" 
           class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700">
            <i class="fas fa-plus mr-2"></i>
            Add SMPP Client
        </a>
    </div>

    <?php if(session('success')): ?>
        <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
            <?php echo e(session('success')); ?>

        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
            <?php echo e(session('error')); ?>

        </div>
    <?php endif; ?>

    <!-- SMPP Clients Grid -->
    <?php if($smppClients->count() > 0): ?>
        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            <?php $__currentLoopData = $smppClients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $client): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <!-- Client Header -->
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-server text-purple-600 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900"><?php echo e($client->system_id); ?></h3>
                                <p class="text-sm text-gray-500">SMPP Client</p>
                            </div>
                        </div>
                        
                        <!-- Status Badges -->
                        <div class="flex flex-col items-end space-y-1">
                            <?php if($client->is_active): ?>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    Active
                                </span>
                            <?php else: ?>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    <i class="fas fa-pause-circle mr-1"></i>
                                    Inactive
                                </span>
                            <?php endif; ?>
                            
                            <?php if($client->is_connected): ?>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <i class="fas fa-wifi mr-1"></i>
                                    Connected
                                </span>
                            <?php else: ?>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <i class="fas fa-wifi-slash mr-1"></i>
                                    Disconnected
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Statistics -->
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div class="text-center p-3 bg-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-gray-900"><?php echo e(number_format($client->messages_sent)); ?></div>
                            <div class="text-xs text-gray-600">Messages Sent</div>
                        </div>
                        <div class="text-center p-3 bg-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-gray-900"><?php echo e(number_format($client->delivery_rate, 1)); ?>%</div>
                            <div class="text-xs text-gray-600">Delivery Rate</div>
                        </div>
                    </div>

                    <!-- Configuration Info -->
                    <div class="space-y-2 mb-4 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Throttle Limit:</span>
                            <span class="font-medium"><?php echo e($client->throttle_limit); ?>/sec</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Failed Messages:</span>
                            <span class="font-medium text-red-600"><?php echo e(number_format($client->messages_failed)); ?></span>
                        </div>
                        <?php if($client->last_connection_at): ?>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Last Connection:</span>
                                <span class="font-medium"><?php echo e($client->last_connection_at->diffForHumans()); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Client Actions -->
                    <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                        <div class="flex space-x-2">
                            <a href="<?php echo e(route('smpp.edit', $client)); ?>" 
                               class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                <i class="fas fa-edit mr-1"></i>
                                Edit
                            </a>
                            <a href="<?php echo e(route('smpp.show', $client)); ?>" 
                               class="text-gray-600 hover:text-gray-800 text-sm font-medium">
                                <i class="fas fa-eye mr-1"></i>
                                View
                            </a>
                        </div>
                        
                        <div class="flex space-x-2">
                            <!-- Toggle Button -->
                            <form action="<?php echo e(route('smpp.toggle', $client)); ?>" method="POST" class="inline">
                                <?php echo csrf_field(); ?>
                                <button type="submit" 
                                        class="text-sm font-medium <?php echo e($client->is_active ? 'text-red-600 hover:text-red-800' : 'text-green-600 hover:text-green-800'); ?>">
                                    <?php if($client->is_active): ?>
                                        <i class="fas fa-pause mr-1"></i>
                                        Deactivate
                                    <?php else: ?>
                                        <i class="fas fa-play mr-1"></i>
                                        Activate
                                    <?php endif; ?>
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Summary Statistics -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <i class="fas fa-server text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Clients</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($smppClients->count()); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Active Clients</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($smppClients->where('is_active', true)->count()); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-purple-100 rounded-lg">
                        <i class="fas fa-paper-plane text-purple-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Messages</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e(number_format($smppClients->sum('messages_sent'))); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-yellow-100 rounded-lg">
                        <i class="fas fa-chart-line text-yellow-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Avg. Delivery Rate</p>
                        <p class="text-2xl font-bold text-gray-900">
                            <?php echo e($smppClients->count() > 0 ? number_format($smppClients->avg('delivery_rate'), 1) : 0); ?>%
                        </p>
                    </div>
                </div>
            </div>
        </div>
    <?php else: ?>
        <!-- Empty State -->
        <div class="text-center py-12">
            <div class="mx-auto h-24 w-24 text-gray-400 mb-4">
                <i class="fas fa-server text-6xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No SMPP clients configured</h3>
            <p class="text-gray-600 mb-6">Get started by adding your first SMPP client for SMS delivery.</p>
            <a href="<?php echo e(route('smpp.create')); ?>" 
               class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700">
                <i class="fas fa-plus mr-2"></i>
                Add Your First SMPP Client
            </a>
        </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/msgbay/resources/views/smpp/index.blade.php ENDPATH**/ ?>