<?php $__env->startSection('page-title', 'Edit Channel'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center space-x-2 text-sm text-gray-600 mb-2">
            <a href="<?php echo e(route('channels.index')); ?>" class="hover:text-blue-600">Channels</a>
            <i class="fas fa-chevron-right text-xs"></i>
            <a href="<?php echo e(route('channels.show', $channel)); ?>" class="hover:text-blue-600"><?php echo e($channel->name); ?></a>
            <i class="fas fa-chevron-right text-xs"></i>
            <span>Edit</span>
        </div>
        <h2 class="text-2xl font-bold text-gray-900">Edit <?php echo e($channel->name); ?></h2>
        <p class="text-gray-600">Update your <?php echo e(str_replace('_', ' ', $channel->type)); ?> channel configuration</p>
    </div>

    <div class="max-w-2xl">
        <form action="<?php echo e(route('channels.update', $channel)); ?>" method="POST">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>
            
            <!-- Channel Name -->
            <div class="mb-6">
                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Channel Name</label>
                <input type="text" id="name" name="name" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                       value="<?php echo e(old('name', $channel->name)); ?>" required>
                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Configuration Fields -->
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Configuration</h3>
                
                <?php switch($channel->type):
                    case ('whatsapp'): ?>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number ID</label>
                                <input type="text" name="config[phone_number_id]" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       value="<?php echo e(old('config.phone_number_id', $channel->config['phone_number_id'] ?? '')); ?>">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Access Token</label>
                                <input type="password" name="config[access_token]" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="Enter new token or leave blank to keep current">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Webhook Verify Token</label>
                                <input type="text" name="config[verify_token]" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       value="<?php echo e(old('config.verify_token', $channel->config['verify_token'] ?? '')); ?>">
                            </div>
                        </div>
                        <?php break; ?>
                        
                    <?php case ('telegram'): ?>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Bot Token</label>
                                <input type="password" name="config[bot_token]" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="Enter new token or leave blank to keep current">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Bot Username</label>
                                <input type="text" name="config[bot_username]" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       value="<?php echo e(old('config.bot_username', $channel->config['bot_username'] ?? '')); ?>">
                            </div>
                        </div>
                        <?php break; ?>
                        
                    <?php case ('messenger'): ?>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Page Access Token</label>
                                <input type="password" name="config[page_access_token]" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="Enter new token or leave blank to keep current">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">App Secret</label>
                                <input type="password" name="config[app_secret]" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="Enter new secret or leave blank to keep current">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Verify Token</label>
                                <input type="text" name="config[verify_token]" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       value="<?php echo e(old('config.verify_token', $channel->config['verify_token'] ?? '')); ?>">
                            </div>
                        </div>
                        <?php break; ?>
                        
                    <?php case ('sms'): ?>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Provider</label>
                                <select name="config[provider]" 
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">Select SMS Provider</option>
                                    <option value="twilio" <?php echo e(($channel->config['provider'] ?? '') === 'twilio' ? 'selected' : ''); ?>>Twilio</option>
                                    <option value="nexmo" <?php echo e(($channel->config['provider'] ?? '') === 'nexmo' ? 'selected' : ''); ?>>Vonage (Nexmo)</option>
                                    <option value="smpp" <?php echo e(($channel->config['provider'] ?? '') === 'smpp' ? 'selected' : ''); ?>>SMPP Gateway</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">API Key</label>
                                <input type="password" name="config[api_key]" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="Enter new API key or leave blank to keep current">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">API Secret</label>
                                <input type="password" name="config[api_secret]" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="Enter new API secret or leave blank to keep current">
                            </div>
                        </div>
                        <?php break; ?>
                        
                    <?php case ('webchat'): ?>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Widget Title</label>
                                <input type="text" name="config[widget_title]" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       value="<?php echo e(old('config.widget_title', $channel->config['widget_title'] ?? 'Chat with us!')); ?>">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Welcome Message</label>
                                <textarea name="config[welcome_message]" rows="3"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo e(old('config.welcome_message', $channel->config['welcome_message'] ?? 'Welcome! How can we help you today?')); ?></textarea>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Primary Color</label>
                                <input type="color" name="config[primary_color]" 
                                       value="<?php echo e(old('config.primary_color', $channel->config['primary_color'] ?? '#3B82F6')); ?>"
                                       class="w-20 h-10 border border-gray-300 rounded-lg">
                            </div>
                        </div>
                        <?php break; ?>
                <?php endswitch; ?>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                <div class="flex space-x-3">
                    <a href="<?php echo e(route('channels.show', $channel)); ?>" 
                       class="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200">
                        Cancel
                    </a>
                    <form action="<?php echo e(route('channels.destroy', $channel)); ?>" method="POST" class="inline"
                          onsubmit="return confirm('Are you sure you want to delete this channel? This action cannot be undone.')">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" 
                                class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700">
                            Delete Channel
                        </button>
                    </form>
                </div>
                <button type="submit" 
                        class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    Update Channel
                </button>
            </div>
        </form>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/msgbay/resources/views/channels/edit.blade.php ENDPATH**/ ?>