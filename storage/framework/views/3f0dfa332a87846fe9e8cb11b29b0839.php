<?php $__env->startSection('page-title', 'Landing Page Editor'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">Landing Page Editor</h2>
                <p class="text-gray-600">Customize your public landing page content and settings</p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="<?php echo e(route('welcome')); ?>" target="_blank" 
                   class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-external-link-alt mr-2"></i>
                    Preview Live
                </a>
                <button onclick="previewChanges()" 
                        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-eye mr-2"></i>
                    Preview Changes
                </button>
            </div>
        </div>
    </div>

    <form action="<?php echo e(route('admin.landing-page.update')); ?>" method="POST" enctype="multipart/form-data" x-data="landingPageEditor()">
        <?php echo csrf_field(); ?>
        <?php echo method_field('PUT'); ?>

        <!-- Hero Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Hero Section</h3>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label for="hero_title" class="block text-sm font-medium text-gray-700 mb-2">
                        Hero Title <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="hero_title" name="hero_title" required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 <?php $__errorArgs = ['hero_title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                           value="<?php echo e(old('hero_title', $content['hero_title'])); ?>">
                    <?php $__errorArgs = ['hero_title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div>
                    <label for="hero_cta_text" class="block text-sm font-medium text-gray-700 mb-2">
                        Call-to-Action Text <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="hero_cta_text" name="hero_cta_text" required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 <?php $__errorArgs = ['hero_cta_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                           value="<?php echo e(old('hero_cta_text', $content['hero_cta_text'])); ?>">
                    <?php $__errorArgs = ['hero_cta_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div class="lg:col-span-2">
                    <label for="hero_subtitle" class="block text-sm font-medium text-gray-700 mb-2">
                        Hero Subtitle <span class="text-red-500">*</span>
                    </label>
                    <textarea id="hero_subtitle" name="hero_subtitle" rows="3" required
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 <?php $__errorArgs = ['hero_subtitle'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"><?php echo e(old('hero_subtitle', $content['hero_subtitle'])); ?></textarea>
                    <?php $__errorArgs = ['hero_subtitle'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div>
                    <label for="hero_cta_url" class="block text-sm font-medium text-gray-700 mb-2">
                        Call-to-Action URL <span class="text-red-500">*</span>
                    </label>
                    <input type="url" id="hero_cta_url" name="hero_cta_url" required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 <?php $__errorArgs = ['hero_cta_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                           value="<?php echo e(old('hero_cta_url', $content['hero_cta_url'])); ?>">
                    <?php $__errorArgs = ['hero_cta_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div>
                    <label for="hero_image" class="block text-sm font-medium text-gray-700 mb-2">
                        Hero Image
                    </label>
                    <input type="file" id="hero_image" name="hero_image" accept="image/*"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 <?php $__errorArgs = ['hero_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                    <?php if($content['hero_image']): ?>
                        <p class="mt-1 text-sm text-gray-600">Current: <?php echo e(basename($content['hero_image'])); ?></p>
                    <?php endif; ?>
                    <?php $__errorArgs = ['hero_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>
        </div>

        <!-- Features Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Features Section</h3>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <div>
                    <label for="features_title" class="block text-sm font-medium text-gray-700 mb-2">
                        Features Title <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="features_title" name="features_title" required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                           value="<?php echo e(old('features_title', $content['features_title'])); ?>">
                </div>

                <div>
                    <label for="features_subtitle" class="block text-sm font-medium text-gray-700 mb-2">
                        Features Subtitle
                    </label>
                    <input type="text" id="features_subtitle" name="features_subtitle"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                           value="<?php echo e(old('features_subtitle', $content['features_subtitle'])); ?>">
                </div>
            </div>

            <!-- Feature Items -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <?php for($i = 1; $i <= 3; $i++): ?>
                <div class="border border-gray-200 rounded-lg p-4">
                    <h4 class="font-medium text-gray-900 mb-3">Feature <?php echo e($i); ?></h4>
                    
                    <div class="space-y-4">
                        <div>
                            <label for="feature_<?php echo e($i); ?>_title" class="block text-sm font-medium text-gray-700 mb-1">
                                Title <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="feature_<?php echo e($i); ?>_title" name="feature_<?php echo e($i); ?>_title" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   value="<?php echo e(old('feature_' . $i . '_title', $content['feature_' . $i . '_title'])); ?>">
                        </div>

                        <div>
                            <label for="feature_<?php echo e($i); ?>_description" class="block text-sm font-medium text-gray-700 mb-1">
                                Description <span class="text-red-500">*</span>
                            </label>
                            <textarea id="feature_<?php echo e($i); ?>_description" name="feature_<?php echo e($i); ?>_description" rows="3" required
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo e(old('feature_' . $i . '_description', $content['feature_' . $i . '_description'])); ?></textarea>
                        </div>

                        <div>
                            <label for="feature_<?php echo e($i); ?>_icon" class="block text-sm font-medium text-gray-700 mb-1">
                                Icon Class <span class="text-red-500">*</span>
                            </label>
                            <select id="feature_<?php echo e($i); ?>_icon" name="feature_<?php echo e($i); ?>_icon" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="fas fa-comments" <?php echo e(old('feature_' . $i . '_icon', $content['feature_' . $i . '_icon']) === 'fas fa-comments' ? 'selected' : ''); ?>>Comments</option>
                                <option value="fas fa-chart-bar" <?php echo e(old('feature_' . $i . '_icon', $content['feature_' . $i . '_icon']) === 'fas fa-chart-bar' ? 'selected' : ''); ?>>Chart Bar</option>
                                <option value="fas fa-users" <?php echo e(old('feature_' . $i . '_icon', $content['feature_' . $i . '_icon']) === 'fas fa-users' ? 'selected' : ''); ?>>Users</option>
                                <option value="fas fa-mobile-alt" <?php echo e(old('feature_' . $i . '_icon', $content['feature_' . $i . '_icon']) === 'fas fa-mobile-alt' ? 'selected' : ''); ?>>Mobile</option>
                                <option value="fas fa-shield-alt" <?php echo e(old('feature_' . $i . '_icon', $content['feature_' . $i . '_icon']) === 'fas fa-shield-alt' ? 'selected' : ''); ?>>Shield</option>
                                <option value="fas fa-rocket" <?php echo e(old('feature_' . $i . '_icon', $content['feature_' . $i . '_icon']) === 'fas fa-rocket' ? 'selected' : ''); ?>>Rocket</option>
                            </select>
                        </div>
                    </div>
                </div>
                <?php endfor; ?>
            </div>
        </div>

        <!-- About Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">About Section</h3>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label for="about_title" class="block text-sm font-medium text-gray-700 mb-2">
                        About Title <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="about_title" name="about_title" required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                           value="<?php echo e(old('about_title', $content['about_title'])); ?>">
                </div>

                <div>
                    <label for="about_image" class="block text-sm font-medium text-gray-700 mb-2">
                        About Image
                    </label>
                    <input type="file" id="about_image" name="about_image" accept="image/*"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <?php if($content['about_image']): ?>
                        <p class="mt-1 text-sm text-gray-600">Current: <?php echo e(basename($content['about_image'])); ?></p>
                    <?php endif; ?>
                </div>

                <div class="lg:col-span-2">
                    <label for="about_content" class="block text-sm font-medium text-gray-700 mb-2">
                        About Content <span class="text-red-500">*</span>
                    </label>
                    <textarea id="about_content" name="about_content" rows="6" required
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo e(old('about_content', $content['about_content'])); ?></textarea>
                </div>
            </div>
        </div>

        <!-- Contact Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Contact Section</h3>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label for="contact_title" class="block text-sm font-medium text-gray-700 mb-2">
                        Contact Title <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="contact_title" name="contact_title" required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                           value="<?php echo e(old('contact_title', $content['contact_title'])); ?>">
                </div>

                <div>
                    <label for="contact_email" class="block text-sm font-medium text-gray-700 mb-2">
                        Contact Email <span class="text-red-500">*</span>
                    </label>
                    <input type="email" id="contact_email" name="contact_email" required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                           value="<?php echo e(old('contact_email', $content['contact_email'])); ?>">
                </div>

                <div class="lg:col-span-2">
                    <label for="contact_subtitle" class="block text-sm font-medium text-gray-700 mb-2">
                        Contact Subtitle
                    </label>
                    <input type="text" id="contact_subtitle" name="contact_subtitle"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                           value="<?php echo e(old('contact_subtitle', $content['contact_subtitle'])); ?>">
                </div>

                <div>
                    <label for="contact_phone" class="block text-sm font-medium text-gray-700 mb-2">
                        Contact Phone
                    </label>
                    <input type="tel" id="contact_phone" name="contact_phone"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                           value="<?php echo e(old('contact_phone', $content['contact_phone'])); ?>">
                </div>

                <div>
                    <label for="contact_address" class="block text-sm font-medium text-gray-700 mb-2">
                        Contact Address
                    </label>
                    <textarea id="contact_address" name="contact_address" rows="3"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo e(old('contact_address', $content['contact_address'])); ?></textarea>
                </div>
            </div>
        </div>

        <!-- SEO Settings -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">SEO Settings</h3>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label for="meta_title" class="block text-sm font-medium text-gray-700 mb-2">
                        Meta Title <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="meta_title" name="meta_title" required maxlength="60"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                           value="<?php echo e(old('meta_title', $content['meta_title'])); ?>">
                    <p class="mt-1 text-xs text-gray-500">Recommended: 50-60 characters</p>
                </div>

                <div>
                    <label for="meta_keywords" class="block text-sm font-medium text-gray-700 mb-2">
                        Meta Keywords
                    </label>
                    <input type="text" id="meta_keywords" name="meta_keywords"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                           value="<?php echo e(old('meta_keywords', $content['meta_keywords'])); ?>">
                    <p class="mt-1 text-xs text-gray-500">Separate keywords with commas</p>
                </div>

                <div class="lg:col-span-2">
                    <label for="meta_description" class="block text-sm font-medium text-gray-700 mb-2">
                        Meta Description <span class="text-red-500">*</span>
                    </label>
                    <textarea id="meta_description" name="meta_description" rows="3" required maxlength="160"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo e(old('meta_description', $content['meta_description'])); ?></textarea>
                    <p class="mt-1 text-xs text-gray-500">Recommended: 150-160 characters</p>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-between pt-6 border-t border-gray-200">
            <div class="flex items-center space-x-4">
                <button type="button" onclick="resetToDefaults()" 
                        class="px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                    Reset to Defaults
                </button>
            </div>
            <div class="flex items-center space-x-3">
                <button type="button" onclick="previewChanges()" 
                        class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-eye mr-2"></i>
                    Preview
                </button>
                <button type="submit" 
                        class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-save mr-2"></i>
                    Save Changes
                </button>
            </div>
        </div>
    </form>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
function landingPageEditor() {
    return {
        // Add any Alpine.js functionality here
    }
}

function previewChanges() {
    const form = document.querySelector('form');
    const formData = new FormData(form);
    
    fetch('<?php echo e(route("admin.landing-page.preview")); ?>', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.open(data.preview_url, '_blank');
        } else {
            alert('Failed to generate preview');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error generating preview');
    });
}

function resetToDefaults() {
    if (confirm('Are you sure you want to reset all content to default values? This will lose all current changes.')) {
        window.location.href = '<?php echo e(route("admin.landing-page.index")); ?>/reset';
    }
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/msgbay/resources/views/admin/landing-page/index.blade.php ENDPATH**/ ?>