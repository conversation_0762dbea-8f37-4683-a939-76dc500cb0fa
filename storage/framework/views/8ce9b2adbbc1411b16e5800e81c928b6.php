<?php $__env->startSection('page-title', 'Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Welcome Section -->
    <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-900">Welcome back, <?php echo e($user->name); ?>!</h2>
        <p class="text-gray-600">Here's what's happening with your MessageBay account today.</p>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Active Channels -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <i class="fas fa-comments text-blue-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Active Channels</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo e($stats['channels']['active'] ?? 0); ?></p>
                    <p class="text-xs text-gray-500"><?php echo e($stats['channels']['total'] ?? 0); ?> total</p>
                </div>
            </div>
        </div>

        <!-- Messages Today -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <i class="fas fa-envelope text-green-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Messages Today</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo e($stats['messages']['today'] ?? 0); ?></p>
                    <?php if(isset($stats['messages']['yesterday']) && $stats['messages']['yesterday'] > 0): ?>
                        <?php
                            $change = $stats['messages']['today'] - $stats['messages']['yesterday'];
                            $changePercent = $stats['messages']['yesterday'] > 0 ? ($change / $stats['messages']['yesterday']) * 100 : 0;
                        ?>
                        <p class="text-xs <?php echo e($change >= 0 ? 'text-green-600' : 'text-red-600'); ?>">
                            <?php echo e($change >= 0 ? '+' : ''); ?><?php echo e(number_format($changePercent, 1)); ?>% from yesterday
                        </p>
                    <?php else: ?>
                        <p class="text-xs text-gray-500"><?php echo e($stats['messages']['this_month'] ?? 0); ?> this month</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Open Chats -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 rounded-lg">
                    <i class="fas fa-headset text-yellow-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Open Chats</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo e($stats['chats']['open'] ?? 0); ?></p>
                    <p class="text-xs text-gray-500"><?php echo e($stats['chats']['assigned'] ?? 0); ?> assigned</p>
                </div>
            </div>
        </div>

        <!-- Subscription Status -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 rounded-lg">
                    <i class="fas fa-crown text-purple-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Plan Status</p>
                    <p class="text-2xl font-bold text-gray-900">
                        <?php if($company->activeSubscription()): ?>
                            Active
                        <?php else: ?>
                            Free Trial
                        <?php endif; ?>
                    </p>
                    <?php if($company->activeSubscription()): ?>
                        <p class="text-xs text-gray-500"><?php echo e($company->activeSubscription()->plan_name); ?></p>
                    <?php else: ?>
                        <p class="text-xs text-gray-500">Upgrade available</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Messages Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Messages Overview (Last 30 Days)</h3>
            <div class="h-64">
                <canvas id="messagesChart"></canvas>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Performance Metrics</h3>
            <div class="space-y-4">
                <!-- Message Delivery Rate -->
                <div>
                    <div class="flex justify-between text-sm mb-1">
                        <span class="text-gray-600">Message Delivery Rate</span>
                        <span class="font-medium">
                            <?php
                                $totalSent = $stats['messages']['sent'] ?? 0;
                                $totalReceived = $stats['messages']['received'] ?? 0;
                                $total = $totalSent + $totalReceived;
                                $deliveryRate = $total > 0 ? ($totalSent / $total) * 100 : 0;
                            ?>
                            <?php echo e(number_format($deliveryRate, 1)); ?>%
                        </span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-green-600 h-2 rounded-full" style="width: <?php echo e($deliveryRate); ?>%"></div>
                    </div>
                </div>

                <!-- Channel Utilization -->
                <div>
                    <div class="flex justify-between text-sm mb-1">
                        <span class="text-gray-600">Channel Utilization</span>
                        <span class="font-medium">
                            <?php
                                $channelUtil = $stats['channels']['total'] > 0 ? ($stats['channels']['active'] / $stats['channels']['total']) * 100 : 0;
                            ?>
                            <?php echo e(number_format($channelUtil, 1)); ?>%
                        </span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full" style="width: <?php echo e($channelUtil); ?>%"></div>
                    </div>
                </div>

                <!-- Chat Resolution Rate -->
                <div>
                    <div class="flex justify-between text-sm mb-1">
                        <span class="text-gray-600">Chat Resolution Rate</span>
                        <span class="font-medium">
                            <?php
                                $totalChats = $stats['chats']['total'] ?? 0;
                                $closedToday = $stats['chats']['closed_today'] ?? 0;
                                $resolutionRate = $totalChats > 0 ? (($totalChats - $stats['chats']['open']) / $totalChats) * 100 : 0;
                            ?>
                            <?php echo e(number_format($resolutionRate, 1)); ?>%
                        </span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-purple-600 h-2 rounded-full" style="width: <?php echo e($resolutionRate); ?>%"></div>
                    </div>
                </div>

                <!-- SMPP Performance -->
                <?php if($stats['smpp']['total_clients'] > 0): ?>
                    <div>
                        <div class="flex justify-between text-sm mb-1">
                            <span class="text-gray-600">SMPP Delivery Rate</span>
                            <span class="font-medium">
                                <?php
                                    $smppTotal = $stats['smpp']['total_sent'] + $stats['smpp']['total_failed'];
                                    $smppRate = $smppTotal > 0 ? ($stats['smpp']['total_sent'] / $smppTotal) * 100 : 0;
                                ?>
                                <?php echo e(number_format($smppRate, 1)); ?>%
                            </span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-yellow-600 h-2 rounded-full" style="width: <?php echo e($smppRate); ?>%"></div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Quick Actions and Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Channel Setup -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Setup</h3>
            <div class="space-y-3">
                <a href="#" class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <i class="fab fa-whatsapp text-green-500 text-xl mr-3"></i>
                    <div>
                        <p class="font-medium text-gray-900">Connect WhatsApp</p>
                        <p class="text-sm text-gray-600">Set up WhatsApp Business API</p>
                    </div>
                </a>
                <a href="#" class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <i class="fab fa-telegram text-blue-500 text-xl mr-3"></i>
                    <div>
                        <p class="font-medium text-gray-900">Connect Telegram</p>
                        <p class="text-sm text-gray-600">Set up Telegram Bot</p>
                    </div>
                </a>
                <a href="#" class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <i class="fas fa-robot text-purple-500 text-xl mr-3"></i>
                    <div>
                        <p class="font-medium text-gray-900">Create Chatbot</p>
                        <p class="text-sm text-gray-600">Build your first chatbot</p>
                    </div>
                </a>
            </div>
        </div>

        <!-- Recent Messages -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Messages</h3>
            <?php if(isset($recentMessages) && $recentMessages->count() > 0): ?>
                <div class="space-y-3">
                    <?php $__currentLoopData = $recentMessages->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $message): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <?php switch($message->channel->type):
                                    case ('whatsapp'): ?>
                                        <i class="fab fa-whatsapp text-green-500"></i>
                                        <?php break; ?>
                                    <?php case ('telegram'): ?>
                                        <i class="fab fa-telegram text-blue-500"></i>
                                        <?php break; ?>
                                    <?php case ('messenger'): ?>
                                        <i class="fab fa-facebook-messenger text-blue-600"></i>
                                        <?php break; ?>
                                    <?php case ('sms'): ?>
                                        <i class="fas fa-sms text-purple-500"></i>
                                        <?php break; ?>
                                    <?php case ('webchat'): ?>
                                        <i class="fas fa-comments text-gray-500"></i>
                                        <?php break; ?>
                                <?php endswitch; ?>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm text-gray-900 truncate"><?php echo e(Str::limit($message->content, 50)); ?></p>
                                <div class="flex items-center space-x-2 text-xs text-gray-500">
                                    <span><?php echo e($message->channel->name); ?></span>
                                    <span>•</span>
                                    <span class="capitalize"><?php echo e($message->direction); ?></span>
                                    <span>•</span>
                                    <span><?php echo e($message->created_at->diffForHumans()); ?></span>
                                </div>
                            </div>
                            <div class="flex-shrink-0">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    <?php echo e($message->status === 'delivered' ? 'bg-green-100 text-green-800' :
                                       ($message->status === 'failed' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800')); ?>">
                                    <?php echo e(ucfirst($message->status)); ?>

                                </span>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                <div class="mt-4 pt-4 border-t border-gray-200">
                    <a href="#" class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                        View all messages →
                    </a>
                </div>
            <?php else: ?>
                <div class="text-center py-6">
                    <i class="fas fa-envelope text-gray-400 text-3xl mb-2"></i>
                    <p class="text-gray-500">No messages yet</p>
                    <p class="text-sm text-gray-400">Messages will appear here once you start communicating</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Help Section -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div class="flex items-center">
            <i class="fas fa-info-circle text-blue-600 text-xl mr-3"></i>
            <div>
                <h3 class="text-lg font-semibold text-blue-900">Need Help Getting Started?</h3>
                <p class="text-blue-700 mt-1">Check out our documentation and tutorials to make the most of MessageBay.</p>
                <div class="mt-4 space-x-4">
                    <a href="#" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700">
                        <i class="fas fa-book mr-2"></i>
                        Documentation
                    </a>
                    <a href="#" class="inline-flex items-center px-4 py-2 bg-white text-blue-600 text-sm font-medium rounded-lg border border-blue-600 hover:bg-blue-50">
                        <i class="fas fa-play mr-2"></i>
                        Video Tutorials
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->startPush('scripts'); ?>
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Messages Chart
const ctx = document.getElementById('messagesChart').getContext('2d');
const messagesChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode($chartData['labels'] ?? [], 15, 512) ?>,
        datasets: [{
            label: 'Messages',
            data: <?php echo json_encode($chartData['messages'] ?? [], 15, 512) ?>,
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.4,
            fill: true
        }, {
            label: 'Chats',
            data: <?php echo json_encode($chartData['chats'] ?? [], 15, 512) ?>,
            borderColor: 'rgb(16, 185, 129)',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    precision: 0
                }
            }
        }
    }
});

// Auto-refresh dashboard data every 30 seconds
setInterval(function() {
    // You can implement AJAX refresh here if needed
    // fetch('/dashboard/widget-data?widget=stats')
    //     .then(response => response.json())
    //     .then(data => {
    //         // Update stats
    //     });
}, 30000);
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/msgbay/resources/views/client/dashboard.blade.php ENDPATH**/ ?>