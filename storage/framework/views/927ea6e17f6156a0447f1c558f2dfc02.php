<?php $__env->startSection('page-title', 'Admin Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
        <p class="text-gray-600">System overview and administrative controls</p>
    </div>

    <!-- System Alerts -->
    <?php if(isset($recentActivity['system_alerts']) && count($recentActivity['system_alerts']) > 0): ?>
        <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-3">System Alerts</h3>
            <div class="space-y-3">
                <?php $__currentLoopData = $recentActivity['system_alerts']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $alert): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="flex items-start p-4 rounded-lg border <?php echo e($alert['type'] === 'error' ? 'bg-red-50 border-red-200' : ($alert['type'] === 'warning' ? 'bg-yellow-50 border-yellow-200' : 'bg-blue-50 border-blue-200')); ?>">
                        <div class="flex-shrink-0">
                            <?php if($alert['type'] === 'error'): ?>
                                <i class="fas fa-exclamation-circle text-red-600"></i>
                            <?php elseif($alert['type'] === 'warning'): ?>
                                <i class="fas fa-exclamation-triangle text-yellow-600"></i>
                            <?php else: ?>
                                <i class="fas fa-info-circle text-blue-600"></i>
                            <?php endif; ?>
                        </div>
                        <div class="ml-3 flex-1">
                            <p class="text-sm font-medium <?php echo e($alert['type'] === 'error' ? 'text-red-800' : ($alert['type'] === 'warning' ? 'text-yellow-800' : 'text-blue-800')); ?>">
                                <?php echo e($alert['message']); ?>

                            </p>
                            <p class="text-xs <?php echo e($alert['type'] === 'error' ? 'text-red-600' : ($alert['type'] === 'warning' ? 'text-yellow-600' : 'text-blue-600')); ?> mt-1">
                                <?php echo e($alert['action']); ?>

                            </p>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- System Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Companies -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <i class="fas fa-building text-blue-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Companies</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo e($stats['companies']['total']); ?></p>
                    <p class="text-xs text-gray-500"><?php echo e($stats['companies']['active']); ?> active</p>
                </div>
            </div>
        </div>

        <!-- Users -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <i class="fas fa-users text-green-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Users</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo e($stats['users']['total']); ?></p>
                    <p class="text-xs text-gray-500"><?php echo e($stats['users']['new_today']); ?> new today</p>
                </div>
            </div>
        </div>

        <!-- Messages -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 rounded-lg">
                    <i class="fas fa-envelope text-purple-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Messages Today</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo e(number_format($stats['messages']['today'])); ?></p>
                    <?php if($stats['messages']['yesterday'] > 0): ?>
                        <?php
                            $change = $stats['messages']['today'] - $stats['messages']['yesterday'];
                            $changePercent = ($change / $stats['messages']['yesterday']) * 100;
                        ?>
                        <p class="text-xs <?php echo e($change >= 0 ? 'text-green-600' : 'text-red-600'); ?>">
                            <?php echo e($change >= 0 ? '+' : ''); ?><?php echo e(number_format($changePercent, 1)); ?>% from yesterday
                        </p>
                    <?php else: ?>
                        <p class="text-xs text-gray-500"><?php echo e(number_format($stats['messages']['this_month'])); ?> this month</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Active Chats -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 rounded-lg">
                    <i class="fas fa-comments text-yellow-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Open Chats</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo e($stats['chats']['open']); ?></p>
                    <p class="text-xs text-gray-500"><?php echo e($stats['chats']['active_today']); ?> active today</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- System Activity Chart -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">System Activity (Last 30 Days)</h3>
            <div class="h-64">
                <canvas id="systemChart"></canvas>
            </div>
        </div>

        <!-- Recent Companies -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Companies</h3>
            <?php if(isset($recentActivity['recent_companies']) && $recentActivity['recent_companies']->count() > 0): ?>
                <div class="space-y-3">
                    <?php $__currentLoopData = $recentActivity['recent_companies']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $company): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-building text-blue-600"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900"><?php echo e($company->name); ?></p>
                                    <p class="text-sm text-gray-500"><?php echo e($company->owner->name ?? 'No owner'); ?></p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-sm text-gray-500"><?php echo e($company->created_at->diffForHumans()); ?></p>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <?php echo e($company->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                    <?php echo e($company->is_active ? 'Active' : 'Inactive'); ?>

                                </span>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                <div class="mt-4 pt-4 border-t border-gray-200">
                    <a href="<?php echo e(route('admin.clients.index')); ?>" class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                        View all companies →
                    </a>
                </div>
            <?php else: ?>
                <p class="text-gray-500 text-center py-4">No companies registered yet</p>
            <?php endif; ?>
        </div>
    </div>

    <!-- System Health and Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- System Health -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">System Health</h3>
            <div class="space-y-4">
                <!-- Message Delivery Rate -->
                <div>
                    <div class="flex justify-between text-sm mb-1">
                        <span class="text-gray-600">Message Success Rate</span>
                        <span class="font-medium">
                            <?php
                                $totalMessages = $stats['messages']['total'];
                                $failedMessages = $stats['messages']['failed'];
                                $successRate = $totalMessages > 0 ? (($totalMessages - $failedMessages) / $totalMessages) * 100 : 100;
                            ?>
                            <?php echo e(number_format($successRate, 1)); ?>%
                        </span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-green-600 h-2 rounded-full" style="width: <?php echo e($successRate); ?>%"></div>
                    </div>
                </div>

                <!-- Channel Connectivity -->
                <div>
                    <div class="flex justify-between text-sm mb-1">
                        <span class="text-gray-600">Channel Connectivity</span>
                        <span class="font-medium">
                            <?php
                                $channelConnectivity = $stats['channels']['total'] > 0 ? ($stats['channels']['connected'] / $stats['channels']['total']) * 100 : 0;
                            ?>
                            <?php echo e(number_format($channelConnectivity, 1)); ?>%
                        </span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full" style="width: <?php echo e($channelConnectivity); ?>%"></div>
                    </div>
                </div>

                <!-- SMPP Performance -->
                <div>
                    <div class="flex justify-between text-sm mb-1">
                        <span class="text-gray-600">SMPP Connectivity</span>
                        <span class="font-medium">
                            <?php
                                $smppConnectivity = $stats['smpp']['total_clients'] > 0 ? ($stats['smpp']['connected_clients'] / $stats['smpp']['total_clients']) * 100 : 0;
                            ?>
                            <?php echo e(number_format($smppConnectivity, 1)); ?>%
                        </span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-purple-600 h-2 rounded-full" style="width: <?php echo e($smppConnectivity); ?>%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div class="space-y-3">
                <a href="<?php echo e(route('admin.clients.index')); ?>" 
                   class="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700">
                    <i class="fas fa-users mr-2"></i>
                    Manage Clients
                </a>
                
                <a href="<?php echo e(route('admin.clients.create')); ?>" 
                   class="w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700">
                    <i class="fas fa-plus mr-2"></i>
                    Add New Client
                </a>
                
                <button onclick="refreshDashboard()" 
                        class="w-full flex items-center justify-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700">
                    <i class="fas fa-sync-alt mr-2"></i>
                    Refresh Data
                </button>
                
                <a href="#" 
                   class="w-full flex items-center justify-center px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-lg hover:bg-purple-700">
                    <i class="fas fa-cog mr-2"></i>
                    System Settings
                </a>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
            <?php if(isset($recentActivity['recent_users']) && $recentActivity['recent_users']->count() > 0): ?>
                <div class="space-y-3">
                    <?php $__currentLoopData = $recentActivity['recent_users']->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-user text-gray-600 text-xs"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate"><?php echo e($user->name); ?></p>
                                <p class="text-xs text-gray-500"><?php echo e($user->company->name ?? 'No company'); ?> • <?php echo e($user->created_at->diffForHumans()); ?></p>
                            </div>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                <?php echo e(ucfirst($user->role)); ?>

                            </span>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php else: ?>
                <p class="text-gray-500 text-center py-4">No recent activity</p>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// System Activity Chart
const ctx = document.getElementById('systemChart').getContext('2d');
const systemChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode($chartData['labels'] ?? [], 15, 512) ?>,
        datasets: [{
            label: 'Messages',
            data: <?php echo json_encode($chartData['messages'] ?? [], 15, 512) ?>,
            borderColor: 'rgb(147, 51, 234)',
            backgroundColor: 'rgba(147, 51, 234, 0.1)',
            tension: 0.4,
            fill: true
        }, {
            label: 'New Users',
            data: <?php echo json_encode($chartData['users'] ?? [], 15, 512) ?>,
            borderColor: 'rgb(34, 197, 94)',
            backgroundColor: 'rgba(34, 197, 94, 0.1)',
            tension: 0.4,
            fill: true
        }, {
            label: 'New Companies',
            data: <?php echo json_encode($chartData['companies'] ?? [], 15, 512) ?>,
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    precision: 0
                }
            }
        }
    }
});

function refreshDashboard() {
    window.location.reload();
}

// Auto-refresh every 5 minutes
setInterval(refreshDashboard, 300000);
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/msgbay/resources/views/admin/dashboard.blade.php ENDPATH**/ ?>