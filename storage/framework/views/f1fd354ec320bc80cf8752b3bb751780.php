<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo $__env->yieldContent('page-title', 'Admin Panel'); ?> - <?php echo e(config('app.name', 'MessageBay')); ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
</head>
<body class="font-sans antialiased bg-gray-100">
    <div class="min-h-screen flex">
        <!-- Sidebar -->
        <div class="w-64 bg-gray-900 text-white flex-shrink-0">
            <!-- Logo -->
            <div class="p-6 border-b border-gray-700">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-crown text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold">Admin Panel</h1>
                        <p class="text-xs text-gray-400">MessageBay</p>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <nav class="mt-6 px-4">
                <div class="space-y-2">
                    <!-- Dashboard -->
                    <a href="<?php echo e(route('admin.dashboard')); ?>" 
                       class="flex items-center px-4 py-2 text-gray-300 rounded-lg hover:bg-gray-800 hover:text-white <?php echo e(request()->routeIs('admin.dashboard') ? 'bg-gray-800 text-white' : ''); ?>">
                        <i class="fas fa-tachometer-alt mr-3"></i>
                        Dashboard
                    </a>

                    <!-- Client Management -->
                    <div x-data="{ open: <?php echo e(request()->routeIs('admin.clients.*') ? 'true' : 'false'); ?> }">
                        <button @click="open = !open" 
                                class="w-full flex items-center justify-between px-4 py-2 text-gray-300 rounded-lg hover:bg-gray-800 hover:text-white <?php echo e(request()->routeIs('admin.clients.*') ? 'bg-gray-800 text-white' : ''); ?>">
                            <div class="flex items-center">
                                <i class="fas fa-users mr-3"></i>
                                Client Management
                            </div>
                            <i class="fas fa-chevron-down transform transition-transform" :class="{ 'rotate-180': open }"></i>
                        </button>
                        <div x-show="open" x-transition class="ml-6 mt-2 space-y-1">
                            <a href="<?php echo e(route('admin.clients.index')); ?>" 
                               class="block px-4 py-2 text-sm text-gray-400 rounded-lg hover:bg-gray-800 hover:text-white <?php echo e(request()->routeIs('admin.clients.index') ? 'bg-gray-800 text-white' : ''); ?>">
                                All Clients
                            </a>
                            <a href="<?php echo e(route('admin.clients.create')); ?>" 
                               class="block px-4 py-2 text-sm text-gray-400 rounded-lg hover:bg-gray-800 hover:text-white <?php echo e(request()->routeIs('admin.clients.create') ? 'bg-gray-800 text-white' : ''); ?>">
                                Add New Client
                            </a>
                        </div>
                    </div>

                    <!-- System Monitoring -->
                    <a href="#" class="flex items-center px-4 py-2 text-gray-300 rounded-lg hover:bg-gray-800 hover:text-white">
                        <i class="fas fa-chart-line mr-3"></i>
                        System Monitoring
                    </a>

                    <!-- User Management -->
                    <a href="#" class="flex items-center px-4 py-2 text-gray-300 rounded-lg hover:bg-gray-800 hover:text-white">
                        <i class="fas fa-user-cog mr-3"></i>
                        User Management
                    </a>

                    <!-- Landing Page Editor -->
                    <a href="#" class="flex items-center px-4 py-2 text-gray-300 rounded-lg hover:bg-gray-800 hover:text-white">
                        <i class="fas fa-edit mr-3"></i>
                        Landing Page Editor
                    </a>

                    <!-- System Settings -->
                    <a href="#" class="flex items-center px-4 py-2 text-gray-300 rounded-lg hover:bg-gray-800 hover:text-white">
                        <i class="fas fa-cog mr-3"></i>
                        System Settings
                    </a>

                    <!-- Reports -->
                    <a href="#" class="flex items-center px-4 py-2 text-gray-300 rounded-lg hover:bg-gray-800 hover:text-white">
                        <i class="fas fa-file-alt mr-3"></i>
                        Reports
                    </a>
                </div>

                <!-- Divider -->
                <div class="border-t border-gray-700 my-6"></div>

                <!-- Quick Access -->
                <div class="space-y-2">
                    <p class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider">Quick Access</p>
                    
                    <a href="<?php echo e(route('dashboard')); ?>" class="flex items-center px-4 py-2 text-gray-300 rounded-lg hover:bg-gray-800 hover:text-white">
                        <i class="fas fa-external-link-alt mr-3"></i>
                        Client View
                    </a>
                    
                    <a href="<?php echo e(route('welcome')); ?>" target="_blank" class="flex items-center px-4 py-2 text-gray-300 rounded-lg hover:bg-gray-800 hover:text-white">
                        <i class="fas fa-globe mr-3"></i>
                        Public Site
                    </a>
                </div>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col">
            <!-- Top Navigation -->
            <header class="bg-white shadow-sm border-b border-gray-200">
                <div class="flex items-center justify-between px-6 py-4">
                    <div class="flex items-center">
                        <h2 class="text-xl font-semibold text-gray-800"><?php echo $__env->yieldContent('page-title', 'Admin Panel'); ?></h2>
                    </div>

                    <div class="flex items-center space-x-4">
                        <!-- Notifications -->
                        <button class="relative p-2 text-gray-600 hover:text-gray-900">
                            <i class="fas fa-bell"></i>
                            <span class="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"></span>
                        </button>

                        <!-- User Menu -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="flex items-center space-x-2 text-gray-700 hover:text-gray-900">
                                <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-gray-600"></i>
                                </div>
                                <span class="font-medium"><?php echo e(Auth::user()->name); ?></span>
                                <i class="fas fa-chevron-down text-xs"></i>
                            </button>

                            <div x-show="open" @click.away="open = false" x-transition
                                 class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-user mr-2"></i>
                                    Profile
                                </a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-cog mr-2"></i>
                                    Settings
                                </a>
                                <div class="border-t border-gray-200 my-1"></div>
                                <form method="POST" action="<?php echo e(route('logout')); ?>">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-sign-out-alt mr-2"></i>
                                        Logout
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto">
                <?php echo $__env->yieldContent('content'); ?>
            </main>
        </div>
    </div>

    <!-- Scripts -->
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH /Users/<USER>/Herd/msgbay/resources/views/layouts/admin.blade.php ENDPATH**/ ?>