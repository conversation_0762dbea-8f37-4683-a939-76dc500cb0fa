<?php $__env->startSection('page-title', 'Channels'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h2 class="text-2xl font-bold text-gray-900">Communication Channels</h2>
            <p class="text-gray-600">Manage your messaging channels and integrations</p>
        </div>
        <a href="<?php echo e(route('channels.create')); ?>" 
           class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700">
            <i class="fas fa-plus mr-2"></i>
            Add Channel
        </a>
    </div>

    <?php if(session('success')): ?>
        <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
            <?php echo e(session('success')); ?>

        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
            <?php echo e(session('error')); ?>

        </div>
    <?php endif; ?>

    <!-- Channels Grid -->
    <?php if($channels->count() > 0): ?>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <?php $__currentLoopData = $channels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $channel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <!-- Channel Header -->
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <?php switch($channel->type):
                                case ('whatsapp'): ?>
                                    <i class="fab fa-whatsapp text-green-500 text-2xl mr-3"></i>
                                    <?php break; ?>
                                <?php case ('telegram'): ?>
                                    <i class="fab fa-telegram text-blue-500 text-2xl mr-3"></i>
                                    <?php break; ?>
                                <?php case ('messenger'): ?>
                                    <i class="fab fa-facebook-messenger text-blue-600 text-2xl mr-3"></i>
                                    <?php break; ?>
                                <?php case ('sms'): ?>
                                    <i class="fas fa-sms text-purple-500 text-2xl mr-3"></i>
                                    <?php break; ?>
                                <?php case ('webchat'): ?>
                                    <i class="fas fa-comments text-gray-500 text-2xl mr-3"></i>
                                    <?php break; ?>
                            <?php endswitch; ?>
                            <div>
                                <h3 class="font-semibold text-gray-900"><?php echo e($channel->name); ?></h3>
                                <p class="text-sm text-gray-500 capitalize"><?php echo e(str_replace('_', ' ', $channel->type)); ?></p>
                            </div>
                        </div>
                        
                        <!-- Status Badges -->
                        <div class="flex flex-col items-end space-y-1">
                            <?php if($channel->is_enabled): ?>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    Enabled
                                </span>
                            <?php else: ?>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    <i class="fas fa-pause-circle mr-1"></i>
                                    Disabled
                                </span>
                            <?php endif; ?>
                            
                            <?php if($channel->is_connected): ?>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <i class="fas fa-wifi mr-1"></i>
                                    Connected
                                </span>
                            <?php else: ?>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <i class="fas fa-wifi-slash mr-1"></i>
                                    Disconnected
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Channel Actions -->
                    <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                        <div class="flex space-x-2">
                            <a href="<?php echo e(route('channels.edit', $channel)); ?>" 
                               class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                <i class="fas fa-edit mr-1"></i>
                                Edit
                            </a>
                            <a href="<?php echo e(route('channels.show', $channel)); ?>" 
                               class="text-gray-600 hover:text-gray-800 text-sm font-medium">
                                <i class="fas fa-eye mr-1"></i>
                                View
                            </a>
                        </div>
                        
                        <div class="flex space-x-2">
                            <!-- Toggle Button -->
                            <form action="<?php echo e(route('channels.toggle', $channel)); ?>" method="POST" class="inline">
                                <?php echo csrf_field(); ?>
                                <button type="submit" 
                                        class="text-sm font-medium <?php echo e($channel->is_enabled ? 'text-red-600 hover:text-red-800' : 'text-green-600 hover:text-green-800'); ?>">
                                    <?php if($channel->is_enabled): ?>
                                        <i class="fas fa-pause mr-1"></i>
                                        Disable
                                    <?php else: ?>
                                        <i class="fas fa-play mr-1"></i>
                                        Enable
                                    <?php endif; ?>
                                </button>
                            </form>
                            
                            <!-- Test Connection -->
                            <form action="<?php echo e(route('channels.test', $channel)); ?>" method="POST" class="inline">
                                <?php echo csrf_field(); ?>
                                <button type="submit" 
                                        class="text-sm font-medium text-purple-600 hover:text-purple-800">
                                    <i class="fas fa-plug mr-1"></i>
                                    Test
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    <?php else: ?>
        <!-- Empty State -->
        <div class="text-center py-12">
            <div class="mx-auto h-24 w-24 text-gray-400 mb-4">
                <i class="fas fa-comments text-6xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No channels configured</h3>
            <p class="text-gray-600 mb-6">Get started by adding your first communication channel.</p>
            <a href="<?php echo e(route('channels.create')); ?>" 
               class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700">
                <i class="fas fa-plus mr-2"></i>
                Add Your First Channel
            </a>
        </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/msgbay/resources/views/channels/index.blade.php ENDPATH**/ ?>