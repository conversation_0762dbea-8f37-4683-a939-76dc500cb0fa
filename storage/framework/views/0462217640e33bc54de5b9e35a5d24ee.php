<?php $__env->startSection('page-title', $client->name . ' - Client Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center space-x-2 text-sm text-gray-600 mb-2">
            <a href="<?php echo e(route('admin.clients.index')); ?>" class="hover:text-blue-600">Client Management</a>
            <i class="fas fa-chevron-right text-xs"></i>
            <span><?php echo e($client->name); ?></span>
        </div>
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-bold text-gray-900"><?php echo e($client->name); ?></h2>
                <div class="flex items-center space-x-4 mt-2">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium <?php echo e($client->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                        <i class="fas fa-circle text-xs mr-2"></i>
                        <?php echo e($client->is_active ? 'Active' : 'Inactive'); ?>

                    </span>
                    <span class="text-sm text-gray-500">
                        Created <?php echo e($client->created_at->diffForHumans()); ?>

                    </span>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="<?php echo e(route('admin.clients.edit', $client)); ?>"
                   class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-edit mr-2"></i>
                    Edit Client
                </a>
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open"
                            class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <div x-show="open" @click.away="open = false" x-transition
                         class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                        <div class="py-1">
                            <?php if($client->is_active): ?>
                                <form action="<?php echo e(route('admin.clients.toggle-status', $client)); ?>" method="POST" class="inline">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('PATCH'); ?>
                                    <button type="submit" class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                        <i class="fas fa-ban mr-2"></i>
                                        Deactivate Account
                                    </button>
                                </form>
                            <?php else: ?>
                                <form action="<?php echo e(route('admin.clients.toggle-status', $client)); ?>" method="POST" class="inline">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('PATCH'); ?>
                                    <button type="submit" class="w-full text-left px-4 py-2 text-sm text-green-600 hover:bg-green-50">
                                        <i class="fas fa-check-circle mr-2"></i>
                                        Activate Account
                                    </button>
                                </form>
                            <?php endif; ?>
                            <button onclick="openPasswordResetModal()"
                                    class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                <i class="fas fa-key mr-2"></i>
                                Reset Owner Password
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Company Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Company Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Company Name</label>
                        <p class="text-gray-900"><?php echo e($client->name); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Email</label>
                        <p class="text-gray-900"><?php echo e($client->email); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Contact Name</label>
                        <p class="text-gray-900"><?php echo e($client->contact_name ?: 'Not specified'); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Phone</label>
                        <p class="text-gray-900"><?php echo e($client->phone ?: 'Not specified'); ?></p>
                    </div>
                    <?php if($client->address): ?>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-500 mb-1">Address</label>
                        <p class="text-gray-900"><?php echo e($client->address); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Owner Information -->
            <?php if($client->owner): ?>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Owner Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Owner Name</label>
                        <p class="text-gray-900"><?php echo e($client->owner->name); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Owner Email</label>
                        <p class="text-gray-900"><?php echo e($client->owner->email); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Account Status</label>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <?php echo e($client->owner->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                            <?php echo e($client->owner->is_active ? 'Active' : 'Inactive'); ?>

                        </span>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Last Login</label>
                        <p class="text-gray-900"><?php echo e($client->owner->last_login_at ? $client->owner->last_login_at->diffForHumans() : 'Never'); ?></p>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Statistics -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Statistics</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="text-center p-4 bg-blue-50 rounded-lg">
                        <div class="text-2xl font-bold text-blue-600"><?php echo e($client->users_count ?? 0); ?></div>
                        <div class="text-sm text-blue-800">Users</div>
                    </div>
                    <div class="text-center p-4 bg-green-50 rounded-lg">
                        <div class="text-2xl font-bold text-green-600"><?php echo e($client->channels_count ?? 0); ?></div>
                        <div class="text-sm text-green-800">Channels</div>
                    </div>
                    <div class="text-center p-4 bg-purple-50 rounded-lg">
                        <div class="text-2xl font-bold text-purple-600"><?php echo e($client->messages_count ?? 0); ?></div>
                        <div class="text-sm text-purple-800">Messages</div>
                    </div>
                    <div class="text-center p-4 bg-yellow-50 rounded-lg">
                        <div class="text-2xl font-bold text-yellow-600"><?php echo e($client->departments_count ?? 0); ?></div>
                        <div class="text-sm text-yellow-800">Departments</div>
                    </div>
                </div>
            </div>

            <!-- Recent Messages -->
            <?php if(isset($client->messages) && $client->messages->count() > 0): ?>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Messages</h3>
                <div class="space-y-4">
                    <?php $__currentLoopData = $client->messages->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $message): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-comment text-blue-600 text-sm"></i>
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between">
                                <p class="text-sm font-medium text-gray-900 truncate">
                                    <?php echo e($message->channel->name ?? 'Unknown Channel'); ?>

                                </p>
                                <p class="text-xs text-gray-500"><?php echo e($message->created_at->diffForHumans()); ?></p>
                            </div>
                            <p class="text-sm text-gray-600 truncate"><?php echo e(Str::limit($message->content, 100)); ?></p>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                <div class="space-y-3">
                    <a href="<?php echo e(route('admin.clients.edit', $client)); ?>"
                       class="w-full flex items-center px-4 py-2 text-sm text-gray-700 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <i class="fas fa-edit mr-3 text-gray-500"></i>
                        Edit Client Details
                    </a>
                    <button onclick="openPasswordResetModal()"
                            class="w-full flex items-center px-4 py-2 text-sm text-gray-700 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <i class="fas fa-key mr-3 text-gray-500"></i>
                        Reset Owner Password
                    </button>
                    <?php if($client->is_active): ?>
                        <form action="<?php echo e(route('admin.clients.toggle-status', $client)); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PATCH'); ?>
                            <button type="submit"
                                    class="w-full flex items-center px-4 py-2 text-sm text-red-600 bg-red-50 rounded-lg hover:bg-red-100 transition-colors">
                                <i class="fas fa-ban mr-3"></i>
                                Deactivate Account
                            </button>
                        </form>
                    <?php else: ?>
                        <form action="<?php echo e(route('admin.clients.toggle-status', $client)); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PATCH'); ?>
                            <button type="submit"
                                    class="w-full flex items-center px-4 py-2 text-sm text-green-600 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                                <i class="fas fa-check-circle mr-3"></i>
                                Activate Account
                            </button>
                        </form>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Account Details -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Account Details</h3>
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Created</label>
                        <p class="text-sm text-gray-900"><?php echo e($client->created_at->format('M j, Y')); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Last Updated</label>
                        <p class="text-sm text-gray-900"><?php echo e($client->updated_at->format('M j, Y')); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Client ID</label>
                        <p class="text-sm text-gray-900 font-mono">#<?php echo e($client->id); ?></p>
                    </div>
                </div>
            </div>

            <!-- System Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">System Information</h3>
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">Storage Used</span>
                        <span class="text-sm text-gray-900">0 MB</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">API Calls (Month)</span>
                        <span class="text-sm text-gray-900">0</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">Plan</span>
                        <span class="text-sm text-gray-900">Standard</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Password Reset Modal -->
<div id="passwordResetModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <form action="<?php echo e(route('admin.clients.reset-password', $client)); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Reset Owner Password</h3>
                    <p class="text-sm text-gray-600 mb-4">
                        Enter a new password for <?php echo e($client->owner->name ?? 'the owner'); ?>.
                    </p>
                    
                    <div class="mb-4">
                        <label for="modal_password" class="block text-sm font-medium text-gray-700 mb-2">
                            New Password
                        </label>
                        <input type="password" id="modal_password" name="password" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div class="mb-6">
                        <label for="modal_password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">
                            Confirm Password
                        </label>
                        <input type="password" id="modal_password_confirmation" name="password_confirmation" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                
                <div class="px-6 py-4 bg-gray-50 rounded-b-lg flex justify-end space-x-3">
                    <button type="button" onclick="closePasswordResetModal()"
                            class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        Reset Password
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
function openPasswordResetModal() {
    document.getElementById('passwordResetModal').classList.remove('hidden');
}

function closePasswordResetModal() {
    document.getElementById('passwordResetModal').classList.add('hidden');
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/msgbay/resources/views/admin/clients/show.blade.php ENDPATH**/ ?>