<?php $__env->startSection('page-title', 'Add SMPP Client'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center space-x-2 text-sm text-gray-600 mb-2">
            <a href="<?php echo e(route('smpp.index')); ?>" class="hover:text-blue-600">SMPP Gateway</a>
            <i class="fas fa-chevron-right text-xs"></i>
            <span>Add Client</span>
        </div>
        <h2 class="text-2xl font-bold text-gray-900">Add New SMPP Client</h2>
        <p class="text-gray-600">Configure a new SMPP client for SMS delivery</p>
    </div>

    <div class="max-w-2xl">
        <form action="<?php echo e(route('smpp.store')); ?>" method="POST" x-data="smppForm()">
            <?php echo csrf_field(); ?>
            
            <!-- System ID -->
            <div class="mb-6">
                <label for="system_id" class="block text-sm font-medium text-gray-700 mb-2">System ID</label>
                <input type="text" id="system_id" name="system_id" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 <?php $__errorArgs = ['system_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                       placeholder="Enter unique system ID" value="<?php echo e(old('system_id')); ?>" required>
                <p class="mt-1 text-sm text-gray-500">Unique identifier for this SMPP client connection</p>
                <?php $__errorArgs = ['system_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Password -->
            <div class="mb-6">
                <label for="password" class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                <input type="password" id="password" name="password" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                       placeholder="Enter secure password" required>
                <p class="mt-1 text-sm text-gray-500">Minimum 6 characters required</p>
                <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Throttle Limit -->
            <div class="mb-6">
                <label for="throttle_limit" class="block text-sm font-medium text-gray-700 mb-2">Throttle Limit (Messages per Second)</label>
                <input type="number" id="throttle_limit" name="throttle_limit" min="1" max="1000"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 <?php $__errorArgs = ['throttle_limit'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                       placeholder="10" value="<?php echo e(old('throttle_limit', 10)); ?>" required>
                <p class="mt-1 text-sm text-gray-500">Maximum messages per second (1-1000)</p>
                <?php $__errorArgs = ['throttle_limit'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Allowed IPs -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">Allowed IP Addresses (Optional)</label>
                <div x-data="{ ips: [''] }">
                    <template x-for="(ip, index) in ips" :key="index">
                        <div class="flex items-center space-x-2 mb-2">
                            <input type="text" :name="'allowed_ips[' + index + ']'" x-model="ips[index]"
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="***********">
                            <button type="button" @click="ips.splice(index, 1)" x-show="ips.length > 1"
                                    class="px-3 py-2 text-red-600 hover:text-red-800">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </template>
                    <button type="button" @click="ips.push('')" 
                            class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        <i class="fas fa-plus mr-1"></i>
                        Add IP Address
                    </button>
                </div>
                <p class="mt-1 text-sm text-gray-500">Leave empty to allow connections from any IP address</p>
                <?php $__errorArgs = ['allowed_ips'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                <?php $__errorArgs = ['allowed_ips.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Information Box -->
            <div class="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-start">
                    <i class="fas fa-info-circle text-blue-600 text-lg mr-3 mt-0.5"></i>
                    <div>
                        <h4 class="text-sm font-medium text-blue-900 mb-1">SMPP Configuration</h4>
                        <div class="text-sm text-blue-800 space-y-1">
                            <p><strong>Server Host:</strong> <?php echo e(config('app.url')); ?></p>
                            <p><strong>Server Port:</strong> 2775 (SMPP)</p>
                            <p><strong>Protocol:</strong> SMPP v3.4</p>
                            <p><strong>Connection Type:</strong> Transceiver</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Notice -->
            <div class="mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div class="flex items-start">
                    <i class="fas fa-exclamation-triangle text-yellow-600 text-lg mr-3 mt-0.5"></i>
                    <div>
                        <h4 class="text-sm font-medium text-yellow-900 mb-1">Security Notice</h4>
                        <p class="text-sm text-yellow-800">
                            Store your SMPP credentials securely. The password will be encrypted and cannot be retrieved once saved.
                            For production use, always configure allowed IP addresses to restrict access.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                <a href="<?php echo e(route('smpp.index')); ?>" 
                   class="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200">
                    Cancel
                </a>
                <button type="submit" 
                        class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    Create SMPP Client
                </button>
            </div>
        </form>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
function smppForm() {
    return {
        // Add any form-specific JavaScript here
    }
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/msgbay/resources/views/smpp/create.blade.php ENDPATH**/ ?>